import { useMutation } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { InferRequestType, InferResponseType } from "hono";

const $post = client.api.ai["apply-style-filter"].$post;

type RequestType = InferRequestType<typeof $post>["json"];
type ResponseType = InferResponseType<typeof $post>;

export const useApplyStyleFilter = () => {
  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      const response = await $post({ json });

      if (!response.ok) {
        throw new Error("Failed to apply style filter");
      }

      return response.json();
    },
  });

  return mutation;
};
