import { fabric } from "fabric";

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: string;
  maintainAspectRatio?: boolean;
}



export const generateThumbnail = (
  canvas: fabric.Canvas,
  options: ThumbnailOptions = {}
): string => {
  const {
    width = 300,
    height = 200,
    quality = 0.7,
    format = "image/jpeg",
    maintainAspectRatio = true
  } = options;

  // Validate canvas
  if (!canvas) {
    console.error("Canvas is null or undefined");
    throw new Error("Canvas is required for thumbnail generation");
  }

  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  if (canvasWidth <= 0 || canvasHeight <= 0) {
    console.error("Canvas has invalid dimensions:", { canvasWidth, canvasHeight });
    throw new Error("Canvas has invalid dimensions");
  }

  // Get the workspace (the main design area)
  const workspace = canvas.getObjects().find(
    (object) => object.name === "clip"
  ) as fabric.Rect;

  if (!workspace) {
    console.warn("No workspace found, using entire canvas");
    // Fallback: use entire canvas with ultra-high quality
    return canvas.toDataURL({
      format,
      quality,
      multiplier: 3, // Ultra-high resolution for crisp quality
    });
  }

  // Get workspace dimensions
  const workspaceWidth = workspace.width! * (workspace.scaleX || 1);
  const workspaceHeight = workspace.height! * (workspace.scaleY || 1);

  // Validate workspace dimensions
  if (workspaceWidth <= 0 || workspaceHeight <= 0) {
    console.error("Workspace has invalid dimensions:", { workspaceWidth, workspaceHeight });
    throw new Error("Workspace has invalid dimensions");
  }

  let thumbnailWidth = width;
  let thumbnailHeight = height;

  if (maintainAspectRatio) {
    // Calculate proper aspect ratio
    const aspectRatio = workspaceWidth / workspaceHeight;

    if (aspectRatio > 1) {
      // Landscape: fit to width
      thumbnailHeight = width / aspectRatio;
    } else {
      // Portrait: fit to height
      thumbnailWidth = height * aspectRatio;
    }
  }

  // Save current state
  const currentTransform = canvas.viewportTransform?.slice();
  const currentZoom = canvas.getZoom();
  const activeObject = canvas.getActiveObject();

  // Reset viewport to identity for clean rendering
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
  canvas.setZoom(1);

  // Temporarily hide any selection or controls for clean thumbnail
  canvas.discardActiveObject();
  canvas.renderAll();

  // Use reasonable multiplier for good quality but smaller file size
  const pixelRatio = window.devicePixelRatio || 1;
  const multiplier = Math.min(2, pixelRatio); // Max 2x for balanced quality and performance

  // Generate ultra-high-quality thumbnail with perfect scaling
  const dataUrl = canvas.toDataURL({
    format,
    quality,
    left: workspace.left,
    top: workspace.top,
    width: workspaceWidth,
    height: workspaceHeight,
    multiplier: multiplier,
    enableRetinaScaling: true,
  });

  // Restore original state
  if (currentTransform) {
    canvas.setViewportTransform(currentTransform);
  }
  canvas.setZoom(currentZoom);
  if (activeObject) {
    canvas.setActiveObject(activeObject);
  }
  canvas.renderAll();

  return dataUrl;
};

export const uploadThumbnail = async (
  dataUrl: string,
  projectId: string
): Promise<string | null> => {
  try {
    // Convert data URL to blob
    const response = await fetch(dataUrl);
    const blob = await response.blob();

    // Create form data
    const formData = new FormData();
    formData.append("files", blob, `thumbnail-${projectId}.jpg`);

    // Upload to UploadThing
    const uploadResponse = await fetch("/api/uploadthing", {
      method: "POST",
      body: formData,
    });

    if (!uploadResponse.ok) {
      throw new Error("Failed to upload thumbnail");
    }

    const uploadResult = await uploadResponse.json();
    return uploadResult[0]?.url || null;
  } catch (error) {
    console.error("Error uploading thumbnail:", error);
    return null;
  }
};

export const generateAndUploadThumbnail = async (
  canvas: fabric.Canvas,
  projectId: string,
  options?: ThumbnailOptions
): Promise<string | null> => {
  try {
    const thumbnailDataUrl = generateThumbnail(canvas, options);
    const thumbnailUrl = await uploadThumbnail(thumbnailDataUrl, projectId);
    return thumbnailUrl;
  } catch (error) {
    console.error("Error generating and uploading thumbnail:", error);
    return null;
  }
};
