{"version": "5.3.0", "objects": [{"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "name": "clip", "selectable": false, "hasControls": false}, {"type": "image", "version": "5.3.0", "originX": "left", "originY": "top", "left": 134.48361053, "top": -284.77960154, "width": 1920, "height": 2339, "fill": "rgb(0,0,0)", "stroke": null, "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 0.512518, "scaleY": 0.512518, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "cropX": 0, "cropY": 0, "selectable": true, "hasControls": true, "src": "https://utfs.io/f/b145a164-cc6a-43d5-9a41-18c082b25ad1-1mlhs8.jpg", "crossOrigin": "anonymous", "filters": []}, {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 94.53915324, "top": -292.96890221, "width": 400, "height": 400, "fill": "rgba(0,0,0,1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 2, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 2.75362509, "scaleY": 3.04770639, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.3, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 301.35259139, "top": 454.86494379, "width": 534.9625079, "height": 90.4, "fill": "rgba(255, 255, 255, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.20410473, "scaleY": 1.20410473, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 500, "fontSize": 80, "text": "CAR SALE", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 95.54142616, "top": 587.97400988, "width": 534.9625079, "height": 90.4, "fill": "rgba(255, 255, 255, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 2.04618206, "scaleY": 2.04618206, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 700, "fontSize": 80, "text": "PROMO", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": -148.44735856, "top": -475.44136381, "width": 300, "height": 300, "fill": "rgba(150, 186, 194, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.95526638, "scaleY": 1.95526638, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.69, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": 857.43124766, "top": 701.28774822, "width": 300, "height": 300, "fill": "rgba(150, 186, 194, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.95526638, "scaleY": 1.95526638, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.69, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 394.52981132, "top": -223.03314538, "width": 804.6484375, "height": 65.54, "fill": "rgba(255, 255, 255, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 0.58354456, "scaleY": 0.58354456, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 500, "fontSize": 58, "text": "www.website.com", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": 513.50190638, "top": -22.22143902, "width": 300, "height": 300, "fill": "rgba(150, 186, 194, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.03818561, "scaleY": 1.03818561, "angle": 15.5411416, "flipX": false, "flipY": false, "opacity": 0.69, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 547.98370009, "top": 109.10048645, "width": 186.71875, "height": 90.4, "fill": "rgba(253, 254, 255, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.06059575, "scaleY": 1.06059575, "angle": 15.5411416, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 700, "fontSize": 80, "text": "30%", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": -41.66579243, "top": 414.23641813, "width": 400, "height": 400, "fill": "rgba(0, 0, 0, 0)", "stroke": "rgba(255, 252, 252, 1)", "strokeWidth": 13, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 3.03729394, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}], "clipPath": {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}}