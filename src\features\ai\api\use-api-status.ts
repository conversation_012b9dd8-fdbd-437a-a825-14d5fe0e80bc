import { useQuery } from "@tanstack/react-query";
import { InferResponseType } from "hono";

import { client } from "@/lib/hono";

type ResponseType = InferResponseType<typeof client.api.ai.status["$get"]>;

export const useApiStatus = () => {
  const query = useQuery<ResponseType, Error>({
    queryKey: ["ai-status"],
    queryFn: async () => {
      const response = await client.api.ai.status.$get();
      return await response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  return query;
};
