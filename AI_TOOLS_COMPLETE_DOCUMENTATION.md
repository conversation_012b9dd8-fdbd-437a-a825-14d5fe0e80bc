# Complete AI Tools Documentation - Next.js Poster Editor

## Overview
This document provides comprehensive documentation for implementing AI tools in a Next.js poster editor application. The system supports multiple AI providers (Together AI, Fal.ai, Replicate) with unified interface and fallback mechanisms.

## Architecture Overview

### Core Components
1. **AI Service Layer** (`src/services/aiService.js`) - Multi-provider AI integration
2. **Client-side AI Service** (`src/services/clientAiService.js`) - Browser-based fallbacks
3. **AI Panel Component** (`src/components/editor/panels/AiPanel.jsx`) - Unified UI interface
4. **Fabric Integration** (`src/fabric/fabric-utils.js`) - Canvas integration utilities
5. **API Routes** - Server-side proxy and processing endpoints

## Dependencies & Installation

### Required NPM Packages
```json
{
  "dependencies": {
    "@fal-ai/client": "^1.6.0",
    "@xenova/transformers": "^2.17.2",
    "fabric": "^6.6.2",
    "replicate": "^1.0.1",
    "together-ai": "^0.20.0",
    "@radix-ui/react-tabs": "^1.1.3",
    "@radix-ui/react-select": "^2.2.5",
    "lucide-react": "^0.486.0",
    "zustand": "^5.0.3"
  }
}
```

### Installation Command
```bash
npm install @fal-ai/client @xenova/transformers fabric replicate together-ai @radix-ui/react-tabs @radix-ui/react-select lucide-react zustand
```

## Environment Configuration

### Required Environment Variables
```env
# Together AI (Primary Provider)
TOGETHER_API_KEY="your-together-api-key-here"
NEXT_PUBLIC_TOGETHER_API_KEY="your-together-api-key-here"

# Fal.ai
FAL_KEY="your-fal-api-key-here"
NEXT_PUBLIC_FAL_KEY="your-fal-api-key-here"

# Replicate (Legacy Support)
REPLICATE_API_TOKEN="your-replicate-api-token-here"
NEXT_PUBLIC_REPLICATE_API_TOKEN="your-replicate-api-token-here"
```

### API Provider Setup

#### Together AI (Recommended)
1. Sign up at [Together.ai](https://together.ai)
2. Navigate to API settings
3. Generate API key
4. Add to environment variables

#### Fal.ai
1. Sign up at [Fal.ai](https://fal.ai)
2. Go to account settings
3. Generate API key
4. Add to environment variables

#### Replicate
1. Sign up at [Replicate.com](https://replicate.com)
2. Go to account settings
3. Generate API token
4. Add to environment variables

## AI Models Configuration

### Supported Models by Provider

#### Together AI Models
- **FLUX Schnell**: `black-forest-labs/FLUX.1-schnell` (Fast generation)
- **FLUX Dev**: `black-forest-labs/FLUX.1-dev` (High quality)
- **FLUX Kontext Max**: `black-forest-labs/FLUX.1-Kontext-Max` (Context-aware)
- **FLUX Kontext Dev**: `black-forest-labs/FLUX.1-Kontext-Dev` (Context development)

#### Fal.ai Models
- **FLUX Pro 1.1**: `fal-ai/flux-pro/v1.1` (Premium quality)
- **FLUX Dev**: `fal-ai/flux/dev` (Development model)
- **BiRefNet**: `fal-ai/birefnet` (Background removal)
- **Clarity Upscaler**: `fal-ai/clarity-upscaler` (Image enhancement)

#### Replicate Models
- **FLUX Schnell**: `black-forest-labs/flux-schnell` (Fast generation)
- **FLUX Dev**: `black-forest-labs/flux-dev` (High quality)
- **Stable Diffusion**: `stability-ai/stable-diffusion` (Classic model)
- **Background Removal**: `cjwbw/rembg` (Background removal)
- **Real-ESRGAN**: `nightmareai/real-esrgan` (Image upscaling)

## Core Service Implementation

### AI Service Structure (`src/services/aiService.js`)

```javascript
import Replicate from 'replicate'
import { fal } from '@fal-ai/client'
import Together from 'together-ai'

// Provider constants
export const AI_PROVIDERS = {
  TOGETHER: 'together',
  FAL: 'fal',
  REPLICATE: 'replicate'
}

// Model configuration
export const AI_MODELS = {
  // Together AI Models
  FLUX_SCHNELL_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-schnell',
    name: 'FLUX Schnell (Together)',
    description: 'Fast FLUX model via Together AI',
    category: 'text-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    default: true
  },
  // ... more models
}

// Initialize clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN,
})

if (process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY) {
  fal.config({
    credentials: process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY,
  })
}

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY,
})
```

### Key Functions

#### Image Generation
```javascript
export async function generateImage(prompt, options = {}) {
  const model = getDefaultModel()
  const defaultOptions = {
    prompt,
    width: options.width || 1024,
    height: options.height || 1024,
    num_inference_steps: options.steps || 28,
    guidance_scale: options.guidance || 7.5
  }

  switch (model.provider) {
    case AI_PROVIDERS.TOGETHER:
      return await generateImageTogether(model.id, defaultOptions)
    case AI_PROVIDERS.FAL:
      return await generateImageFal(model.id, defaultOptions)
    case AI_PROVIDERS.REPLICATE:
      return await generateImageReplicate(model.id, defaultOptions)
  }
}
```

#### Background Removal
```javascript
export async function removeBackground(imageUrl, provider = AI_PROVIDERS.FAL) {
  switch (provider) {
    case AI_PROVIDERS.FAL:
      const result = await fal.subscribe(AI_MODELS.REMOVE_BG_FAL.id, {
        input: { image_url: imageUrl }
      })
      return result.image?.url || result.image
    
    case AI_PROVIDERS.REPLICATE:
      const output = await replicate.run(AI_MODELS.REMOVE_BG.id, {
        input: { image: imageUrl }
      })
      return output
  }
}
```

#### Image Upscaling
```javascript
export async function upscaleImage(imageUrl, scale = 2, provider = AI_PROVIDERS.FAL) {
  switch (provider) {
    case AI_PROVIDERS.FAL:
      const result = await fal.subscribe(AI_MODELS.UPSCALE_FAL.id, {
        input: {
          image_url: imageUrl,
          scale_factor: scale
        }
      })
      return result.image?.url || result.image
  }
}
```

## UI Component Implementation

### AI Panel Component (`src/components/editor/panels/AiPanel.jsx`)

```jsx
import { useState } from "react";
import { useEditorStore } from "@/store";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  generateImage,
  removeBackground,
  upscaleImage,
  generateImageVariations,
  isApiConfigured,
  getAvailableModels,
  AI_PROVIDERS
} from "@/services/aiService";

export default function AiPanel() {
  const { canvas, selectedObject } = useEditorStore();
  const [activeTab, setActiveTab] = useState("generate");
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    try {
      const imageUrl = await generateImage(prompt, {
        width: 1024,
        height: 1024
      });
      
      await addImageToCanvasUtil(imageUrl);
    } catch (error) {
      console.error('Generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate">Generate</TabsTrigger>
          <TabsTrigger value="edit">Edit</TabsTrigger>
          <TabsTrigger value="enhance">Enhance</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="generate">
          {/* Generation UI */}
        </TabsContent>
        
        <TabsContent value="edit">
          {/* Editing UI */}
        </TabsContent>
        
        <TabsContent value="enhance">
          {/* Enhancement UI */}
        </TabsContent>
        
        <TabsContent value="settings">
          {/* Settings UI */}
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## Canvas Integration

### Fabric.js Integration (`src/fabric/fabric-utils.js`)

```javascript
export const addImageToCanvas = async (canvas, imageUrl) => {
  if (!canvas) return null;

  try {
    const fabric = await getFabric();
    
    const image = await fabric.FabricImage.fromURL(imageUrl, {
      crossOrigin: 'anonymous'
    });

    // Set image properties
    image.set({
      id: `image-${Date.now()}`,
      padding: 10,
      cornerSize: 10,
      imageSmoothing: true,
      strokeWidth: 0,
      stroke: null,
    });

    // Scale if too large
    const maxDimension = 400;
    if (image.width > maxDimension || image.height > maxDimension) {
      const scale = Math.min(maxDimension / image.width, maxDimension / image.height);
      image.scale(scale);
    }

    canvas.add(image);
    canvas.setActiveObject(image);
    canvas.renderAll();

    return image;
  } catch (error) {
    console.error("Error adding image:", error);
    return null;
  }
};
```

## API Routes

### Image Proxy Route (`src/app/api/proxy-image/route.js`)

```javascript
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json({ error: 'Missing image URL' }, { status: 400 });
    }

    // Validate Together AI URL for security
    if (!imageUrl.startsWith('https://api.together.ai/')) {
      return NextResponse.json({ error: 'Invalid image URL' }, { status: 400 });
    }

    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';

    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json({ error: 'Failed to proxy image' }, { status: 500 });
  }
}
```

## Client-side Fallbacks

### Transformers.js Integration (`src/services/clientAiService.js`)

```javascript
let transformersLoaded = false;
let pipeline = null;

export async function loadTransformers() {
  if (transformersLoaded) return pipeline;
  
  try {
    const { pipeline: createPipeline } = await import('@xenova/transformers');
    pipeline = await createPipeline('image-segmentation', 'Xenova/detr-resnet-50-panoptic');
    transformersLoaded = true;
    return pipeline;
  } catch (error) {
    console.error('Failed to load Transformers.js:', error);
    throw error;
  }
}

export async function removeBackgroundClientSide(imageUrl) {
  const pipe = await loadTransformers();
  // Implementation for client-side background removal
  return processedImageUrl;
}

export function isClientSideBackgroundRemovalAvailable() {
  return typeof window !== 'undefined' && 'WebGL2RenderingContext' in window;
}
```

## State Management

### Zustand Store Integration (`src/store/index.js`)

```javascript
import { create } from "zustand";

export const useEditorStore = create((set, get) => ({
  // Canvas state
  canvas: null,
  selectedObject: null,
  
  // AI panel state
  activePanel: 'select',
  aiPanelOpen: false,
  
  setCanvas: (canvas) => set({ canvas }),
  setSelectedObject: (obj) => set({ selectedObject: obj }),
  setActivePanel: (panel) => set({ activePanel: panel }),
  setAiPanelOpen: (open) => set({ aiPanelOpen: open }),
  
  // Utility functions
  markAsModified: () => {
    // Mark canvas as modified for save functionality
  }
}));
```

## Error Handling & Fallbacks

### Provider Detection
```javascript
export function isApiConfigured(provider = null) {
  if (provider) {
    switch (provider) {
      case AI_PROVIDERS.TOGETHER:
        const togetherToken = process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY;
        return !!togetherToken && togetherToken !== 'your-together-api-key-here'

      case AI_PROVIDERS.FAL:
        const falToken = process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY;
        return !!falToken && falToken !== 'your-fal-api-key-here'

      case AI_PROVIDERS.REPLICATE:
        const replicateToken = process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN;
        return !!replicateToken && replicateToken !== 'your-replicate-api-token-here'
    }
  }

  return isApiConfigured(AI_PROVIDERS.TOGETHER) ||
         isApiConfigured(AI_PROVIDERS.FAL) ||
         isApiConfigured(AI_PROVIDERS.REPLICATE)
}
```

### Graceful Degradation
```javascript
export async function removeBackground(imageUrl, provider = AI_PROVIDERS.FAL) {
  try {
    // Try API-based removal first
    if (isApiConfigured(provider)) {
      return await removeBackgroundAPI(imageUrl, provider);
    }

    // Fall back to client-side processing
    if (isClientSideBackgroundRemovalAvailable()) {
      return await removeBackgroundClientSide(imageUrl);
    }

    throw new Error('No background removal method available');
  } catch (error) {
    console.error('Background removal failed:', error);
    throw error;
  }
}
```

## Complete API Implementation Details

### Together AI Implementation
```javascript
async function generateImageTogether(modelId, options) {
  const apiKey = process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY;

  if (!apiKey) {
    throw new Error('Together AI API key not configured');
  }

  const isKontextModel = modelId.includes('kontext');

  if (isKontextModel && !options.image_url) {
    throw new Error('FLUX Kontext models require an input image');
  }

  const requestData = {
    model: modelId,
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    steps: options.num_inference_steps || 28,
    n: 1,
    response_format: "url"
  };

  if (isKontextModel && options.image_url) {
    requestData.image_url = options.image_url;
  }

  const response = await together.images.create(requestData);

  if (!response.data || !response.data[0] || !response.data[0].url) {
    throw new Error('Invalid response from Together AI');
  }

  return response.data[0].url;
}
```

### Fal.ai Implementation
```javascript
async function generateImageFal(modelId, options) {
  const result = await fal.subscribe(modelId, {
    input: {
      prompt: options.prompt,
      image_size: {
        width: options.width,
        height: options.height
      },
      num_inference_steps: options.num_inference_steps || 28,
      guidance_scale: options.guidance_scale || 7.5,
      num_images: 1,
      enable_safety_checker: true
    }
  });

  if (!result.images || !result.images[0] || !result.images[0].url) {
    throw new Error('Invalid response from Fal.ai');
  }

  return result.images[0].url;
}
```

### Replicate Implementation
```javascript
async function generateImageReplicate(modelId, options) {
  const input = {
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    num_inference_steps: options.num_inference_steps || 28,
    guidance_scale: options.guidance_scale || 7.5,
    num_outputs: 1
  };

  const output = await replicate.run(modelId, { input });

  if (!output || !output[0]) {
    throw new Error('Invalid response from Replicate');
  }

  return Array.isArray(output) ? output[0] : output;
}
```

## Style Filters Implementation

### AI Style Filters
```javascript
export const AI_STYLE_FILTERS = {
  ARTISTIC: {
    name: 'Artistic',
    prompt: 'artistic style, painterly, expressive brushstrokes',
    icon: '🎨'
  },
  VINTAGE: {
    name: 'Vintage',
    prompt: 'vintage style, retro, aged, nostalgic',
    icon: '📸'
  },
  CYBERPUNK: {
    name: 'Cyberpunk',
    prompt: 'cyberpunk style, neon, futuristic, digital',
    icon: '🌆'
  },
  WATERCOLOR: {
    name: 'Watercolor',
    prompt: 'watercolor painting, soft, flowing, translucent',
    icon: '🖌️'
  },
  OIL_PAINT: {
    name: 'Oil Paint',
    prompt: 'oil painting, thick brushstrokes, rich colors',
    icon: '🖼️'
  },
  SKETCH: {
    name: 'Sketch',
    prompt: 'pencil sketch, hand-drawn, artistic lines',
    icon: '✏️'
  }
};

export async function applyStyleFilter(imageUrl, style, customPrompt = '') {
  const styleFilter = AI_STYLE_FILTERS[style];
  if (!styleFilter) {
    throw new Error(`Unknown style filter: ${style}`);
  }

  const prompt = customPrompt || `Transform this image in ${styleFilter.prompt} style`;

  return await generateImageVariations(imageUrl, prompt);
}
```

## Complete UI Components

### Settings Tab Implementation
```jsx
<TabsContent value="settings" className="m-0 p-4 space-y-4">
  <div className="space-y-4">
    <div>
      <h4 className="font-medium text-gray-900 mb-3">AI Provider Status</h4>
      <div className="space-y-3">
        {/* Together AI */}
        <div className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h5 className="font-medium text-gray-900">Together AI</h5>
              <p className="text-sm text-gray-600">FLUX models, fast generation</p>
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium ${
              isApiConfigured(AI_PROVIDERS.TOGETHER)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isApiConfigured(AI_PROVIDERS.TOGETHER) ? 'Configured' : 'Not Configured'}
            </div>
          </div>

          {!isApiConfigured(AI_PROVIDERS.TOGETHER) && (
            <div className="text-sm text-gray-600 space-y-2">
              <p>To configure Together AI:</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>Sign up at <a href="https://together.ai" target="_blank" className="text-blue-600 hover:underline">Together.ai</a></li>
                <li>Go to your API settings</li>
                <li>Generate an API key</li>
                <li>Add TOGETHER_API_KEY to your .env.local file</li>
              </ol>
            </div>
          )}
        </div>

        {/* Fal.ai */}
        <div className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h5 className="font-medium text-gray-900">Fal.ai</h5>
              <p className="text-sm text-gray-600">FLUX Pro, background removal, upscaling</p>
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium ${
              isApiConfigured(AI_PROVIDERS.FAL)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isApiConfigured(AI_PROVIDERS.FAL) ? 'Configured' : 'Not Configured'}
            </div>
          </div>

          {!isApiConfigured(AI_PROVIDERS.FAL) && (
            <div className="text-sm text-gray-600 space-y-2">
              <p>To configure Fal.ai:</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>Sign up at <a href="https://fal.ai" target="_blank" className="text-blue-600 hover:underline">Fal.ai</a></li>
                <li>Go to your account settings</li>
                <li>Generate an API key</li>
                <li>Add FAL_KEY to your .env.local file</li>
              </ol>
            </div>
          )}
        </div>

        {/* Replicate */}
        <div className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h5 className="font-medium text-gray-900">Replicate</h5>
              <p className="text-sm text-gray-600">Legacy support, various models</p>
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium ${
              isApiConfigured(AI_PROVIDERS.REPLICATE)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isApiConfigured(AI_PROVIDERS.REPLICATE) ? 'Configured' : 'Not Configured'}
            </div>
          </div>

          {!isApiConfigured(AI_PROVIDERS.REPLICATE) && (
            <div className="text-sm text-gray-600 space-y-2">
              <p>To configure Replicate:</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>Sign up at <a href="https://replicate.com" target="_blank" className="text-blue-600 hover:underline">Replicate.com</a></li>
                <li>Go to your account settings</li>
                <li>Generate an API token</li>
                <li>Add REPLICATE_API_TOKEN to your .env.local file</li>
              </ol>
            </div>
          )}
        </div>
      </div>
    </div>

    {/* Provider Priority */}
    <div className="border-t pt-4">
      <h4 className="font-medium text-gray-900 mb-2">Provider Priority</h4>
      <p className="text-sm text-gray-600 mb-3">
        The system automatically selects providers in this order:
      </p>
      <ol className="list-decimal list-inside text-sm text-gray-600 space-y-1">
        <li>Together AI (Recommended for speed and quality)</li>
        <li>Fal.ai (Best for professional features)</li>
        <li>Replicate (Legacy support)</li>
      </ol>
    </div>

    {/* Client-side Fallbacks */}
    <div className="border-t pt-4">
      <h4 className="font-medium text-gray-900 mb-2">Client-side Fallbacks</h4>
      <p className="text-sm text-gray-600 mb-3">
        When API providers are not configured, some features fall back to browser-based processing:
      </p>
      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
        <li>Background removal using Transformers.js</li>
        <li>Basic image upscaling using canvas</li>
        <li>Simple image filters and effects</li>
      </ul>
      <div className={`mt-2 px-2 py-1 rounded text-xs font-medium ${
        isClientSideBackgroundRemovalAvailable()
          ? 'bg-green-100 text-green-800'
          : 'bg-yellow-100 text-yellow-800'
      }`}>
        Client-side processing: {isClientSideBackgroundRemovalAvailable() ? 'Available' : 'Limited (WebGL required)'}
      </div>
    </div>
  </div>
</TabsContent>
```

## Testing Implementation

### Unit Tests for AI Service
```javascript
// __tests__/aiService.test.js
import { generateImage, removeBackground, upscaleImage, isApiConfigured } from '../src/services/aiService';

describe('AI Service', () => {
  beforeEach(() => {
    // Mock environment variables
    process.env.TOGETHER_API_KEY = 'test-key';
    process.env.FAL_KEY = 'test-key';
    process.env.REPLICATE_API_TOKEN = 'test-key';
  });

  test('should detect configured APIs', () => {
    expect(isApiConfigured('together')).toBe(true);
    expect(isApiConfigured('fal')).toBe(true);
    expect(isApiConfigured('replicate')).toBe(true);
  });

  test('should generate image with valid prompt', async () => {
    const mockResponse = 'https://example.com/generated-image.png';

    // Mock the API call
    jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ data: [{ url: mockResponse }] })
    });

    const result = await generateImage('test prompt');
    expect(result).toBe(mockResponse);
  });

  test('should handle API errors gracefully', async () => {
    jest.spyOn(global, 'fetch').mockRejectedValue(new Error('API Error'));

    await expect(generateImage('test prompt')).rejects.toThrow('Failed to generate image');
  });
});
```

### Integration Tests
```javascript
// __tests__/integration/aiPanel.test.js
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AiPanel from '../src/components/editor/panels/AiPanel';
import { useEditorStore } from '../src/store';

// Mock the store
jest.mock('../src/store');
jest.mock('../src/services/aiService');

describe('AI Panel Integration', () => {
  beforeEach(() => {
    useEditorStore.mockReturnValue({
      canvas: { add: jest.fn(), setActiveObject: jest.fn(), renderAll: jest.fn() },
      selectedObject: null
    });
  });

  test('should render all tabs', () => {
    render(<AiPanel />);

    expect(screen.getByText('Generate')).toBeInTheDocument();
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('Enhance')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  test('should generate image on button click', async () => {
    const mockGenerateImage = jest.fn().mockResolvedValue('https://example.com/image.png');
    require('../src/services/aiService').generateImage = mockGenerateImage;

    render(<AiPanel />);

    const promptInput = screen.getByPlaceholderText(/describe the image/i);
    const generateButton = screen.getByText('Generate Image');

    fireEvent.change(promptInput, { target: { value: 'test prompt' } });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(mockGenerateImage).toHaveBeenCalledWith('test prompt', expect.any(Object));
    });
  });
});
```

## Performance Optimization

### Image Caching Strategy
```javascript
// src/utils/imageCache.js
class ImageCache {
  constructor(maxSize = 50) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  get(key) {
    const value = this.cache.get(key);
    if (value) {
      // Move to end (LRU)
      this.cache.delete(key);
      this.cache.set(key, value);
    }
    return value;
  }

  has(key) {
    return this.cache.has(key);
  }

  clear() {
    this.cache.clear();
  }
}

export const imageCache = new ImageCache();

// Usage in AI service
export async function generateImage(prompt, options = {}) {
  const cacheKey = `${prompt}-${JSON.stringify(options)}`;

  if (imageCache.has(cacheKey)) {
    return imageCache.get(cacheKey);
  }

  const result = await generateImageInternal(prompt, options);
  imageCache.set(cacheKey, result);

  return result;
}
```

### Lazy Loading for Transformers.js
```javascript
// src/services/clientAiService.js
let transformersPromise = null;

export async function loadTransformers() {
  if (!transformersPromise) {
    transformersPromise = import('@xenova/transformers').then(async ({ pipeline }) => {
      return await pipeline('image-segmentation', 'Xenova/detr-resnet-50-panoptic');
    });
  }
  return transformersPromise;
}
```

## Deployment Configuration

### Environment Variables for Production
```env
# Production .env
NODE_ENV=production

# Together AI (Primary)
TOGETHER_API_KEY=your-production-together-key
NEXT_PUBLIC_TOGETHER_API_KEY=your-production-together-key

# Fal.ai (Secondary)
FAL_KEY=your-production-fal-key
NEXT_PUBLIC_FAL_KEY=your-production-fal-key

# Replicate (Fallback)
REPLICATE_API_TOKEN=your-production-replicate-token
NEXT_PUBLIC_REPLICATE_API_TOKEN=your-production-replicate-token

# Security
NEXTAUTH_SECRET=your-production-secret
NEXTAUTH_URL=https://your-domain.com

# Database
DATABASE_URL=your-production-database-url
```

### Next.js Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'api.together.ai',
      'fal.media',
      'replicate.delivery',
      'images.unsplash.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.together.ai',
      },
      {
        protocol: 'https',
        hostname: '**.fal.media',
      },
      {
        protocol: 'https',
        hostname: '**.replicate.delivery',
      }
    ]
  },
  experimental: {
    serverComponentsExternalPackages: ['@xenova/transformers']
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }
    return config;
  }
};

module.exports = nextConfig;
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. CORS Issues with Generated Images
**Problem**: Generated images fail to load due to CORS restrictions.

**Solution**: Use the proxy route for Together AI images:
```javascript
// Instead of using the direct URL
const imageUrl = response.data[0].url;

// Use the proxy route
const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
await addImageToCanvas(canvas, proxyUrl);
```

#### 2. API Rate Limiting
**Problem**: API requests fail due to rate limits.

**Solution**: Implement retry logic with exponential backoff:
```javascript
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;

      const delay = Math.pow(2, i) * 1000; // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

export async function generateImage(prompt, options = {}) {
  return retryWithBackoff(() => generateImageInternal(prompt, options));
}
```

#### 3. Memory Issues with Large Images
**Problem**: Browser crashes or becomes unresponsive with large images.

**Solution**: Implement image resizing before canvas addition:
```javascript
async function resizeImageIfNeeded(imageUrl, maxWidth = 2048, maxHeight = 2048) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      if (img.width <= maxWidth && img.height <= maxHeight) {
        resolve(imageUrl);
        return;
      }

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      const ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      resolve(canvas.toDataURL('image/jpeg', 0.9));
    };

    img.onerror = reject;
    img.src = imageUrl;
  });
}
```

#### 4. Transformers.js Loading Issues
**Problem**: Client-side AI features fail to load.

**Solution**: Add proper error handling and fallbacks:
```javascript
export async function loadTransformers() {
  try {
    if (typeof window === 'undefined') {
      throw new Error('Transformers.js only works in browser');
    }

    if (!('WebGL2RenderingContext' in window)) {
      throw new Error('WebGL2 not supported');
    }

    const { pipeline } = await import('@xenova/transformers');
    return await pipeline('image-segmentation', 'Xenova/detr-resnet-50-panoptic');
  } catch (error) {
    console.warn('Failed to load Transformers.js:', error);
    return null;
  }
}
```

### Debug Mode
```javascript
// Add to your AI service for debugging
const DEBUG_MODE = process.env.NODE_ENV === 'development';

function debugLog(message, data = null) {
  if (DEBUG_MODE) {
    console.log(`[AI Service Debug] ${message}`, data);
  }
}

export async function generateImage(prompt, options = {}) {
  debugLog('Starting image generation', { prompt, options });

  try {
    const result = await generateImageInternal(prompt, options);
    debugLog('Image generation successful', { resultUrl: result });
    return result;
  } catch (error) {
    debugLog('Image generation failed', { error: error.message });
    throw error;
  }
}
```

## Security Considerations

### API Key Protection
```javascript
// Validate API keys on server-side only
export function validateApiKeys() {
  const requiredKeys = [
    'TOGETHER_API_KEY',
    'FAL_KEY',
    'REPLICATE_API_TOKEN'
  ];

  const missingKeys = requiredKeys.filter(key => !process.env[key]);

  if (missingKeys.length > 0) {
    console.warn('Missing API keys:', missingKeys);
  }

  return missingKeys.length === 0;
}
```

### Input Sanitization
```javascript
export function sanitizePrompt(prompt) {
  // Remove potentially harmful content
  const sanitized = prompt
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();

  // Limit length
  return sanitized.substring(0, 1000);
}
```

### Rate Limiting
```javascript
// Simple in-memory rate limiter
const rateLimiter = new Map();

export function checkRateLimit(userId, maxRequests = 10, windowMs = 60000) {
  const now = Date.now();
  const userRequests = rateLimiter.get(userId) || [];

  // Remove old requests
  const validRequests = userRequests.filter(time => now - time < windowMs);

  if (validRequests.length >= maxRequests) {
    throw new Error('Rate limit exceeded');
  }

  validRequests.push(now);
  rateLimiter.set(userId, validRequests);

  return true;
}
```

## Monitoring and Analytics

### Usage Tracking
```javascript
// src/utils/analytics.js
export function trackAIUsage(action, provider, metadata = {}) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'ai_tool_usage', {
      action,
      provider,
      ...metadata
    });
  }

  // Also log to your analytics service
  console.log('AI Usage:', { action, provider, metadata, timestamp: new Date().toISOString() });
}

// Usage in AI service
export async function generateImage(prompt, options = {}) {
  const startTime = Date.now();

  try {
    const result = await generateImageInternal(prompt, options);

    trackAIUsage('image_generation_success', options.provider, {
      duration: Date.now() - startTime,
      prompt_length: prompt.length
    });

    return result;
  } catch (error) {
    trackAIUsage('image_generation_error', options.provider, {
      duration: Date.now() - startTime,
      error: error.message
    });

    throw error;
  }
}
```

This completes the comprehensive AI tools documentation. The system provides a robust, multi-provider AI integration with fallbacks, error handling, and production-ready features.
