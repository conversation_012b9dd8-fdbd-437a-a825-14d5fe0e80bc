// Simple script to check environment variables
// Environment Variables Check - logging removed

const requiredVars = [
  'NEXT_PUBLIC_UNSPLASH_ACCESS_KEY',
  'DATABASE_URL',
  'AUTH_SECRET',
  'UPLOADTHING_SECRET',
  'UPLOADTHING_APP_ID'
];

const optionalVars = [
  'REPLICATE_API_TOKEN',
  'AUTH_GITHUB_ID',
  'AUTH_GITHUB_SECRET',
  'AUTH_GOOGLE_ID',
  'AUTH_GOOGLE_SECRET',
  'STRIPE_SECRET_KEY',
  'STRIPE_PRICE_ID',
  'STRIPE_WEBHOOK_SECRET'
];

// Required Variables check - logging removed
requiredVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅ SET' : '❌ MISSING';
  // Variable status logging removed
});

// Optional Variables check - logging removed
optionalVars.forEach(varName => {
  const value = process.env[varName];
  const status = value ? '✅ SET' : '⚠️  NOT SET';
  // Variable status logging removed
});

// Environment check complete - logging removed
