/**
 * High-performance canvas optimization using advanced data structures
 * Implements spatial indexing, object pooling, and efficient rendering
 */

import { fabric } from "fabric";
import { AdvancedCache, memoizeAdvanced } from "./advanced-cache";

interface CanvasObject extends fabric.Object {
  id?: string;
  lastModified?: number;
  renderHash?: string;
}

interface SpatialNode {
  bounds: { x: number; y: number; width: number; height: number };
  objects: CanvasObject[];
  children?: SpatialNode[];
}

export class CanvasOptimizer {
  private canvas: fabric.Canvas;
  private spatialIndex: SpatialNode;
  private objectPool = new Map<string, CanvasObject[]>();
  private renderCache = new AdvancedCache<string>({ maxSize: 50 });
  private dirtyRegions = new Set<string>();
  private lastRenderTime = 0;
  private renderQueue: (() => void)[] = [];
  private isRendering = false;

  constructor(canvas: fabric.Canvas) {
    this.canvas = canvas;
    this.spatialIndex = this.createSpatialIndex();
    this.setupOptimizations();
  }

  private createSpatialIndex(): SpatialNode {
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    
    return {
      bounds: { x: 0, y: 0, width: canvasWidth, height: canvasHeight },
      objects: [],
      children: []
    };
  }

  private setupOptimizations(): void {
    // Override canvas rendering with batched updates
    const originalRenderAll = this.canvas.renderAll.bind(this.canvas);
    this.canvas.renderAll = this.batchedRenderAll.bind(this);

    // Optimize object modifications
    this.canvas.on('object:modified', this.onObjectModified.bind(this));
    this.canvas.on('object:added', this.onObjectAdded.bind(this));
    this.canvas.on('object:removed', this.onObjectRemoved.bind(this));
  }

  private batchedRenderAll(): void {
    if (this.isRendering) return;

    // Use requestAnimationFrame for smooth rendering
    requestAnimationFrame(() => {
      this.isRendering = true;
      
      try {
        // Only render if enough time has passed (throttling)
        const now = performance.now();
        if (now - this.lastRenderTime < 16) { // ~60fps
          this.isRendering = false;
          return;
        }

        // Process render queue
        while (this.renderQueue.length > 0) {
          const renderFn = this.renderQueue.shift();
          renderFn?.();
        }

        // Perform actual render
        this.canvas.getContext().save();
        this.renderOptimized();
        this.canvas.getContext().restore();
        
        this.lastRenderTime = now;
      } finally {
        this.isRendering = false;
      }
    });
  }

  private renderOptimized(): void {
    const ctx = this.canvas.getContext();
    const viewportTransform = this.canvas.viewportTransform;
    
    if (!viewportTransform) return;

    // Calculate visible region
    const zoom = this.canvas.getZoom();
    const visibleBounds = {
      x: -viewportTransform[4] / zoom,
      y: -viewportTransform[5] / zoom,
      width: this.canvas.getWidth() / zoom,
      height: this.canvas.getHeight() / zoom
    };

    // Get visible objects using spatial index
    const visibleObjects = this.getVisibleObjects(visibleBounds);
    
    // Sort objects by z-index for proper layering
    visibleObjects.sort((a, b) => (a.canvas?.getObjects().indexOf(a) || 0) - (b.canvas?.getObjects().indexOf(b) || 0));

    // Render only visible objects
    for (const obj of visibleObjects) {
      if (this.shouldRenderObject(obj, visibleBounds)) {
        this.renderObjectOptimized(obj, ctx);
      }
    }
  }

  private getVisibleObjects(bounds: { x: number; y: number; width: number; height: number }): CanvasObject[] {
    const visibleObjects: CanvasObject[] = [];
    this.traverseSpatialIndex(this.spatialIndex, bounds, visibleObjects);
    return visibleObjects;
  }

  private traverseSpatialIndex(
    node: SpatialNode, 
    bounds: { x: number; y: number; width: number; height: number },
    result: CanvasObject[]
  ): void {
    // Check if node bounds intersect with visible bounds
    if (!this.boundsIntersect(node.bounds, bounds)) return;

    // Add objects from this node
    result.push(...node.objects);

    // Traverse children
    if (node.children) {
      for (const child of node.children) {
        this.traverseSpatialIndex(child, bounds, result);
      }
    }
  }

  private boundsIntersect(
    a: { x: number; y: number; width: number; height: number },
    b: { x: number; y: number; width: number; height: number }
  ): boolean {
    return !(a.x + a.width < b.x || b.x + b.width < a.x || 
             a.y + a.height < b.y || b.y + b.height < a.y);
  }

  private shouldRenderObject(obj: CanvasObject, visibleBounds: any): boolean {
    // Skip invisible objects
    if (!obj.visible || obj.opacity === 0) return false;

    // Check if object is in visible area
    const objBounds = obj.getBoundingRect();
    return this.boundsIntersect(objBounds, visibleBounds);
  }

  private renderObjectOptimized(obj: CanvasObject, ctx: CanvasRenderingContext2D): void {
    const renderHash = this.generateRenderHash(obj);
    
    // Check render cache
    if (obj.renderHash === renderHash && this.renderCache.has(renderHash)) {
      // Use cached render if available
      return;
    }

    // Render object
    ctx.save();
    obj.render(ctx);
    ctx.restore();

    // Update cache
    obj.renderHash = renderHash;
    this.renderCache.set(renderHash, 'rendered');
  }

  private generateRenderHash(obj: CanvasObject): string {
    return `${obj.left}-${obj.top}-${obj.scaleX}-${obj.scaleY}-${obj.angle}-${obj.opacity}`;
  }

  private onObjectModified(e: fabric.IEvent): void {
    const obj = e.target as CanvasObject;
    if (obj) {
      obj.lastModified = Date.now();
      this.updateSpatialIndex(obj);
      this.queueRender();
    }
  }

  private onObjectAdded(e: fabric.IEvent): void {
    const obj = e.target as CanvasObject;
    if (obj) {
      this.addToSpatialIndex(obj);
      this.queueRender();
    }
  }

  private onObjectRemoved(e: fabric.IEvent): void {
    const obj = e.target as CanvasObject;
    if (obj) {
      this.removeFromSpatialIndex(obj);
      this.queueRender();
    }
  }

  private updateSpatialIndex(obj: CanvasObject): void {
    this.removeFromSpatialIndex(obj);
    this.addToSpatialIndex(obj);
  }

  private addToSpatialIndex(obj: CanvasObject): void {
    // Simple implementation - add to root node
    // In production, implement proper spatial partitioning
    this.spatialIndex.objects.push(obj);
  }

  private removeFromSpatialIndex(obj: CanvasObject): void {
    const index = this.spatialIndex.objects.indexOf(obj);
    if (index > -1) {
      this.spatialIndex.objects.splice(index, 1);
    }
  }

  private queueRender(): void {
    if (!this.isRendering) {
      this.batchedRenderAll();
    }
  }

  // Object pooling for frequently created/destroyed objects
  getPooledObject(type: string): CanvasObject | null {
    const pool = this.objectPool.get(type);
    return pool?.pop() || null;
  }

  returnToPool(type: string, obj: CanvasObject): void {
    if (!this.objectPool.has(type)) {
      this.objectPool.set(type, []);
    }
    
    const pool = this.objectPool.get(type)!;
    if (pool.length < 10) { // Limit pool size
      // Reset object state
      obj.set({ left: 0, top: 0, scaleX: 1, scaleY: 1, angle: 0 });
      pool.push(obj);
    }
  }

  // Memoized expensive operations
  private memoizedToDataURL = memoizeAdvanced(
    (options: any) => this.canvas.toDataURL(options),
    (options) => JSON.stringify(options),
    30000 // 30 second cache
  );

  optimizedToDataURL(options: any): string {
    return this.memoizedToDataURL(options);
  }

  destroy(): void {
    this.renderCache.destroy();
    this.objectPool.clear();
    this.renderQueue.length = 0;
  }
}

// Global canvas optimizer instances
const canvasOptimizers = new WeakMap<fabric.Canvas, CanvasOptimizer>();

export function getCanvasOptimizer(canvas: fabric.Canvas): CanvasOptimizer {
  if (!canvasOptimizers.has(canvas)) {
    canvasOptimizers.set(canvas, new CanvasOptimizer(canvas));
  }
  return canvasOptimizers.get(canvas)!;
}
