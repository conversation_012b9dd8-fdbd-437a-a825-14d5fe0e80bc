"use client"

import { useState, useRef } from 'react'
import { Upload, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface SimpleUploadButtonProps {
  onUploadComplete?: (result: { url: string; name: string; size: number; type: string }) => void
  onUploadError?: (error: string) => void
  accept?: string
  maxSize?: number
  className?: string
  children?: React.ReactNode
  disabled?: boolean
}

export function SimpleUploadButton({
  onUploadComplete,
  onUploadError,
  accept = 'image/*',
  maxSize = 4 * 1024 * 1024, // 4MB default
  className,
  children,
  disabled = false
}: SimpleUploadButtonProps) {
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / 1024 / 1024)
      onUploadError?.(`File size too large. Maximum ${maxSizeMB}MB allowed.`)
      return
    }

    // Validate file type
    if (accept !== '*' && !file.type.match(accept.replace('*', '.*'))) {
      onUploadError?.('Invalid file type.')
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      onUploadComplete?.(result)
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Upload error:', error)
      onUploadError?.(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const handleButtonClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click()
    }
  }

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled || isUploading}
      />
      <Button
        onClick={handleButtonClick}
        disabled={disabled || isUploading}
        className={cn("w-full", className)}
        variant="outline"
      >
        {isUploading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Uploading...
          </>
        ) : (
          <>
            <Upload className="w-4 h-4 mr-2" />
            {children || 'Upload Image'}
          </>
        )}
      </Button>
    </>
  )
}
