Warning: Extra attributes from the server: webcrx 
overrideMethod @ installHook.js:1
 [Fast Refresh] rebuilding
 [Fast Refresh] done in 6285ms
 [Fast Refresh] rebuilding
 [Fast Refresh] done in 3157ms
 [Fast Refresh] rebuilding
 [Fast Refresh] done in 54409ms
:3000/api/projects:1  Failed to load resource: the server responded with a status of 404 (Not Found)
 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. 
overrideMethod @ installHook.js:1
 Default AI model selected: FLUX Dev (Together)
 Default AI model selected: FLUX Dev (Together)
 useLoadState: Attempting to load initial state
 useLoadState: Parsed JSON data: Object
 useLoadState: Canvas dimensions: Object
 useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
 useLoadState: JSON loaded successfully, objects count: 4
 useLoadState: Calling autoZoom...
 useLoadState: Template loading complete
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation throttled, too soon since last generation
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Generating image with model: black-forest-labs/FLUX.1-dev
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Thumbnail generation already in progress, skipping...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Data URL is valid image
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Fallback thumbnail storage successful
 Data URL is valid image
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Adding generated image to canvas
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
10e4f116-7921-4959-950d-e0ab1cad72df:1 Access to image at 'https://api.together.ai/shrt/ANesD78MmWXSqMdl' from origin 'http://localhost:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
api.together.ai/shrt/ANesD78MmWXSqMdl:1  Failed to load resource: net::ERR_FAILED
 Error loading https://api.together.ai/shrt/ANesD78MmWXSqMdl
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Image successfully added to canvas and selected
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Generating initial thumbnail...
 Thumbnail generation throttled, too soon since last generation
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Generating initial thumbnail...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Data URL is valid image
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Generating initial thumbnail...
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
 Thumbnail generation already in progress, skipping...
 Generating initial thumbnail...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Generating initial thumbnail...
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Force generating thumbnail for testing...
 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Skipping render for object with zero dimensions: klass
overrideMethod @ installHook.js:1
 Generated thumbnail data URL length: 432226
 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
 Is valid data URL: true
 Thumbnail generated, updating project...
 Uploading thumbnail to external storage...
 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
 Failed to upload thumbnail: 
overrideMethod @ installHook.js:1
 Falling back to data URL storage...
 Fallback thumbnail storage successful
 Thumbnail generation throttled, too soon since last generation
 Generating initial thumbnail...
 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
editor.tsx:109 Generating initial thumbnail...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
editor.tsx:109 Generating initial thumbnail...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:109 Generating initial thumbnail...
editor.tsx:109 Generating initial thumbnail...
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
hook.js:608 Skipping render for object with zero dimensions: klass
overrideMethod @ hook.js:608
hot-reloader-client.js:187 [Fast Refresh] rebuilding
hot-reloader-client.js:44 [Fast Refresh] done in 3118ms
