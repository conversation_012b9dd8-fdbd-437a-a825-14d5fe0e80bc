Warning: Extra attributes from the server: webcrx 
overrideMethod @ installHook.js:1
 [Fast Refresh] rebuilding
 [Fast Refresh] done in 97020ms
hot-reloader-client.js:187 [Fast Refresh] rebuilding
hot-reloader-client.js:44 [Fast Refresh] done in 3324ms
hook.js:608 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:42:26)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Checkboard (Checkboard.js:16:20)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:42:26)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:42:26)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
hook.js:608 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at CircleSwatch (CircleSwatch.js:15:20)
    at span (<anonymous>)
    at Hover (hover.js:33:7)
    at div (<anonymous>)
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at Editor (editor.tsx:42:26)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
AiPanel.jsx:109 Default AI model selected: FLUX Dev (Together)
AiPanel.jsx:109 Default AI model selected: FLUX Dev (Together)
use-load-state.ts:25 useLoadState: Attempting to load initial state
use-load-state.ts:28 useLoadState: Parsed JSON data: Object
use-load-state.ts:37 useLoadState: Canvas dimensions: Object
use-load-state.ts:44 useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
use-load-state.ts:46 useLoadState: JSON loaded successfully, objects count: 4
use-load-state.ts:77 useLoadState: Calling autoZoom...
use-load-state.ts:79 useLoadState: Template loading complete
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
AiPanel.jsx:247 Generating variations for selected image
editor.tsx:109 Generating initial thumbnail...
:3000/api/ai/generate-variations:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
hook.js:608 Image variations failed: Error: Failed to generate variations
    at Object.mutationFn (use-generate-variations.ts:16:15)
overrideMethod @ hook.js:608
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
:3000/api/uploadthing:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
hook.js:608 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
hot-reloader-client.js:187 [Fast Refresh] rebuilding
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
hot-reloader-client.js:187 [Fast Refresh] rebuilding
use-thumbnail-generator.ts:76 Data URL is valid image
editor.tsx:109 Generating initial thumbnail...
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
editor.tsx:109 Generating initial thumbnail...
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
use-thumbnail-generator.ts:102  POST http://localhost:3000/api/uploadthing 400 (Bad Request)
eval @ use-thumbnail-generator.ts:102
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
use-thumbnail-generator.ts:153 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
eval @ use-thumbnail-generator.ts:153
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
use-thumbnail-generator.ts:155 Falling back to data URL storage...
hot-reloader-client.js:44 [Fast Refresh] done in 6428ms
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:102  POST http://localhost:3000/api/uploadthing 400 (Bad Request)
eval @ use-thumbnail-generator.ts:102
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:153 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
eval @ use-thumbnail-generator.ts:153
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:39 Thumbnail generation already in progress, skipping...
editor.tsx:109 Generating initial thumbnail...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:102  POST http://localhost:3000/api/uploadthing 400 (Bad Request)
eval @ use-thumbnail-generator.ts:102
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:153 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
eval @ use-thumbnail-generator.ts:153
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
editor.tsx:115 Force generating thumbnail for testing...
use-thumbnail-generator.ts:52 Generating thumbnail for project: 10e4f116-7921-4959-950d-e0ab1cad72df
use-thumbnail-generator.ts:67 Generated thumbnail data URL length: 432226
use-thumbnail-generator.ts:68 Thumbnail data URL preview: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzkAAARMCAYAAAC6Wl3lAAAAAXNSR0IArs4c6QAAIABJREFUeF7svW...
use-thumbnail-generator.ts:72 Is valid data URL: true
use-thumbnail-generator.ts:87 Thumbnail generated, updating project...
use-thumbnail-generator.ts:90 Uploading thumbnail to external storage...
use-thumbnail-generator.ts:76 Data URL is valid image
use-thumbnail-generator.ts:102  POST http://localhost:3000/api/uploadthing 400 (Bad Request)
eval @ use-thumbnail-generator.ts:102
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:153 Failed to upload thumbnail: Error: Upload failed: 400
    at eval (use-thumbnail-generator.ts:108:17)
    at async eval (use-thumbnail-generator.ts:189:5)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
eval @ use-thumbnail-generator.ts:153
await in eval
eval @ use-thumbnail-generator.ts:189
eval @ editor.tsx:116
setTimeout
generateInitialThumbnail @ editor.tsx:114
eval @ editor.tsx:120
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
performSyncWorkOnRoot @ react-dom.development.js:24870
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
commitRootImpl @ react-dom.development.js:26369
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
use-thumbnail-generator.ts:155 Falling back to data URL storage...
use-thumbnail-generator.ts:164 Fallback thumbnail storage successful
use-thumbnail-generator.ts:44 Thumbnail generation throttled, too soon since last generation
editor.tsx:109 Generating initial thumbnail...
