import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { eq, and } from "drizzle-orm";

import { db } from "@/db/drizzle";
import { projects, users } from "@/db/schema";

const customizeSchema = z.object({
  templateId: z.string(),
  customizations: z.record(z.object({
    type: z.enum(['text', 'image']),
    value: z.string(),
  })),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, customizations } = customizeSchema.parse(body);

    // Fetch the template
    const templateData = await db
      .select({
        id: projects.id,
        name: projects.name,
        json: projects.json,
        width: projects.width,
        height: projects.height,
        isCustomizable: projects.isCustomizable,
        editableLayers: projects.editableLayers,
        isPublic: projects.isPublic,
      })
      .from(projects)
      .where(
        and(
          eq(projects.id, templateId),
          eq(projects.isPublic, true),
          eq(projects.isCustomizable, true)
        )
      );

    if (templateData.length === 0) {
      return NextResponse.json(
        { error: "Template not found or not customizable" },
        { status: 404 }
      );
    }

    const template = templateData[0];

    if (!template.editableLayers) {
      return NextResponse.json(
        { error: "Template has no editable layers" },
        { status: 400 }
      );
    }

    // Parse the template JSON and editable layers
    const templateJson = JSON.parse(template.json);
    const editableLayers = JSON.parse(template.editableLayers);

    // Apply customizations to the template
    const customizedJson = { ...templateJson };
    
    // Update objects in the template based on customizations
    if (customizedJson.objects) {
      customizedJson.objects = customizedJson.objects.map((obj: any) => {
        const customization = customizations[obj.id];
        if (customization && editableLayers.some((layer: any) => layer.id === obj.id)) {
          if (customization.type === 'text' && obj.type === 'textbox') {
            return {
              ...obj,
              text: customization.value,
            };
          } else if (customization.type === 'image' && (obj.type === 'image' || obj.type === 'rect' || obj.type === 'circle')) {
            // For image replacement, we would need to handle the image upload and processing
            // For now, we'll just update a src property if it exists
            return {
              ...obj,
              src: customization.value,
            };
          }
        }
        return obj;
      });
    }

    // Generate a session ID for this customization
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // In a real implementation, you would:
    // 1. Render the customized template to an image using a service like Puppeteer or Canvas API
    // 2. Store the customized JSON and generated image temporarily
    // 3. Return URLs for preview and download

    // For now, we'll return a mock response
    const mockPreviewUrl = `/api/templates/preview/${sessionId}`;
    const mockDownloadUrl = `/api/templates/download/${sessionId}`;

    // Store the customization session (in a real app, you'd use Redis or a database)
    // For demo purposes, we'll just return the URLs

    return NextResponse.json({
      success: true,
      sessionId,
      previewUrl: mockPreviewUrl,
      downloadUrl: mockDownloadUrl,
      customizedJson: JSON.stringify(customizedJson),
    });

  } catch (error) {
    console.error("Template customization error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
