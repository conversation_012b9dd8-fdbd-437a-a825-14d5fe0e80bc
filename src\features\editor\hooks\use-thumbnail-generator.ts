import { useCallback, useRef } from "react";
import debounce from "lodash.debounce";
import { useQueryClient } from "@tanstack/react-query";

import { Editor } from "@/features/editor/types";
import { client } from "@/lib/hono";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const queryClient = useQueryClient();
  const lastThumbnailRef = useRef<string | null>(null);
  const isGeneratingRef = useRef(false);
  const lastGenerationTimeRef = useRef(0);
  const MIN_GENERATION_INTERVAL = 5000; // Minimum 5 seconds between generations

  const generateThumbnail = useCallback(async () => {
    if (!editor) {
      console.warn("No editor available for thumbnail generation");
      return;
    }

    if (!editor.canvas) {
      console.warn("No canvas available for thumbnail generation");
      return;
    }

    // Throttling: prevent too frequent generation
    const now = Date.now();
    if (isGeneratingRef.current) {
      console.log("Thumbnail generation already in progress, skipping...");
      return;
    }

    if (now - lastGenerationTimeRef.current < MIN_GENERATION_INTERVAL) {
      console.log("Thumbnail generation throttled, too soon since last generation");
      return;
    }

    isGeneratingRef.current = true;
    lastGenerationTimeRef.current = now;

    try {
      console.log("Generating thumbnail for project:", projectId);

      // Generate thumbnail data URL with optimized settings
      const thumbnailDataUrl = editor.generateThumbnail({
        width: 300,
        height: 200,
        quality: 0.6,
        format: "image/jpeg",
      });

      if (!thumbnailDataUrl) {
        console.error("Failed to generate thumbnail data URL");
        return;
      }

      console.log("Generated thumbnail data URL length:", thumbnailDataUrl.length);
      console.log("Thumbnail data URL preview:", thumbnailDataUrl.substring(0, 100) + "...");

      // Test if data URL is valid
      const isValidDataUrl = thumbnailDataUrl.startsWith('data:image/');
      console.log("Is valid data URL:", isValidDataUrl);

      // Test if we can create an image from it
      const testImg = new Image();
      testImg.onload = () => console.log("Data URL is valid image");
      testImg.onerror = (e) => console.error("Data URL is invalid:", e);
      testImg.src = thumbnailDataUrl;

      // Skip if thumbnail hasn't changed significantly
      if (lastThumbnailRef.current === thumbnailDataUrl) {
        console.log("Thumbnail unchanged, skipping update");
        return;
      }

      lastThumbnailRef.current = thumbnailDataUrl;
      console.log("Thumbnail generated, updating project...");

      // Upload thumbnail to external storage instead of storing base64 in database
      console.log("Uploading thumbnail to external storage...");

      try {
        // Convert data URL to blob
        const response = await fetch(thumbnailDataUrl);
        const blob = await response.blob();

        // Create form data for upload
        const formData = new FormData();
        formData.append("files", blob, `thumbnail-${projectId}.jpg`);

        // Upload to UploadThing
        const uploadResponse = await fetch("/api/uploadthing", {
          method: "POST",
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.status}`);
        }

        const uploadResult = await uploadResponse.json();
        const thumbnailUrl = uploadResult[0]?.url;

        if (!thumbnailUrl) {
          throw new Error("No URL returned from upload");
        }

        console.log("Thumbnail uploaded successfully, URL:", thumbnailUrl);

        // Update project with thumbnail URL (not data URL)
        const patchResponse = await client.api.projects[":id"].$patch({
          param: { id: projectId },
          json: {
            thumbnailUrl: thumbnailUrl,
          },
        });
        console.log("PATCH response status:", patchResponse.status);

        if (patchResponse.ok) {
          console.log("Thumbnail updated successfully");
          const responseData = await patchResponse.json();
          console.log("Response data:", responseData);

          // Invalidate projects query to refresh the UI
          console.log("Invalidating queries after thumbnail update...");
          queryClient.invalidateQueries({ queryKey: ["projects"] });

          // Also invalidate the individual project query
          queryClient.invalidateQueries({ queryKey: ["project", { id: projectId }] });

          // Force refetch of projects data
          queryClient.refetchQueries({ queryKey: ["projects"] });

          console.log("Query invalidation completed");

          onThumbnailGenerated?.(thumbnailUrl);
        } else {
          console.error("Failed to update project with thumbnail URL:", patchResponse.status, patchResponse.statusText);
          const errorText = await patchResponse.text();
          console.error("Error response:", errorText);
        }
      } catch (uploadError) {
        console.error("Failed to upload thumbnail:", uploadError);
        // Fallback: store as data URL if upload fails
        console.log("Falling back to data URL storage...");
        const fallbackResponse = await client.api.projects[":id"].$patch({
          param: { id: projectId },
          json: {
            thumbnailUrl: thumbnailDataUrl,
          },
        });

        if (fallbackResponse.ok) {
          console.log("Fallback thumbnail storage successful");
          onThumbnailGenerated?.(thumbnailDataUrl);
        } else {
          console.error("Fallback thumbnail storage also failed");
        }
      }
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    } finally {
      isGeneratingRef.current = false;
    }
  }, [editor, projectId, onThumbnailGenerated]);

  // Debounced version to avoid too frequent thumbnail generation
  const debouncedGenerateThumbnail = useCallback(
    debounce(generateThumbnail, 3000), // Generate thumbnail 3 seconds after last change
    [generateThumbnail]
  );

  const forceRegenerateThumbnail = useCallback(async () => {
    // Reset the last thumbnail to force regeneration
    lastThumbnailRef.current = null;
    // Reset throttling for forced regeneration
    lastGenerationTimeRef.current = 0;
    isGeneratingRef.current = false;
    await generateThumbnail();
  }, [generateThumbnail]);

  return {
    generateThumbnail,
    debouncedGenerateThumbnail,
    forceRegenerateThumbnail,
  };
};
