-- Performance optimization indexes for the Canva clone project
-- Run this migration to improve query performance

-- Index for projects table - most common queries
CREATE INDEX IF NOT EXISTS idx_projects_user_id_updated_at ON project (userId, updatedAt DESC);
CREATE INDEX IF NOT EXISTS idx_projects_user_id_created_at ON project (userId, createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_projects_is_template ON project (isTemplate) WHERE isTemplate = true;
CREATE INDEX IF NOT EXISTS idx_projects_is_public ON project (isPublic) WHERE isPublic = true;
CREATE INDEX IF NOT EXISTS idx_projects_is_customizable ON project (isCustomizable) WHERE isCustomizable = true;

-- Composite index for template queries
CREATE INDEX IF NOT EXISTS idx_projects_template_public ON project (isTemplate, isPublic, isCustomizable) 
WHERE isTemplate = true AND isPublic = true;

-- Index for user authentication queries
CREATE INDEX IF NOT EXISTS idx_users_email ON "user" (email);

-- Index for subscription queries
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscription (userId);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscription (status);

-- Comments explaining the indexes:
-- 1. idx_projects_user_id_updated_at: Optimizes the main projects list query with pagination
-- 2. idx_projects_user_id_created_at: Alternative sorting for projects
-- 3. idx_projects_is_template: Speeds up template filtering
-- 4. idx_projects_is_public: Speeds up public project queries
-- 5. idx_projects_is_customizable: Speeds up customizable template queries
-- 6. idx_projects_template_public: Composite index for template marketplace queries
-- 7. idx_users_email: Speeds up login/authentication
-- 8. idx_subscriptions_user_id: Speeds up subscription status checks
-- 9. idx_subscriptions_status: Speeds up active subscription queries
