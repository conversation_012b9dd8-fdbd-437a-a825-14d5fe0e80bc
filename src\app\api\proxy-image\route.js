import { NextResponse } from 'next/server'

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const imageUrl = searchParams.get('url')

    if (!imageUrl) {
      return NextResponse.json({ error: 'Missing image URL' }, { status: 400 })
    }

    // Validate URL for security - only allow specific domains
    const allowedDomains = [
      'api.together.ai',
      'fal.media',
      'replicate.delivery',
      'images.unsplash.com',
      'cdn.openai.com'
    ]

    let isAllowed = false
    try {
      const url = new URL(imageUrl)
      isAllowed = allowedDomains.some(domain => 
        url.hostname === domain || url.hostname.endsWith(`.${domain}`)
      )
    } catch (error) {
      return NextResponse.json({ error: 'Invalid image URL' }, { status: 400 })
    }

    if (!isAllowed) {
      return NextResponse.json({ error: 'Image URL not allowed' }, { status: 403 })
    }

    // Fetch the image
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache'
      },
      // Add timeout
      signal: AbortSignal.timeout(30000) // 30 seconds
    })

    if (!response.ok) {
      // Failed to fetch image - logging removed
      return NextResponse.json(
        { error: `Failed to fetch image: ${response.status}` }, 
        { status: response.status }
      )
    }

    // Verify content type
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.startsWith('image/')) {
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }

    // Get image buffer
    const imageBuffer = await response.arrayBuffer()
    
    // Check file size (limit to 10MB)
    if (imageBuffer.byteLength > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'Image too large' }, { status: 413 })
    }

    // Return proxied image with appropriate headers
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Content-Length': imageBuffer.byteLength.toString()
      },
    })
  } catch (error) {
    // Proxy error - logging removed
    
    // Handle specific error types
    if (error.name === 'AbortError') {
      return NextResponse.json({ error: 'Request timeout' }, { status: 408 })
    }
    
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return NextResponse.json({ error: 'Network error' }, { status: 502 })
    }
    
    return NextResponse.json({ error: 'Failed to proxy image' }, { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  })
}
