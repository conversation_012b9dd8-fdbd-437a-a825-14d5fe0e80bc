"use client";

import Link from "next/link";
import { Globe, ArrowRight, Eye } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export const PublicGalleryBanner = () => {
  return (
    <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-purple-100 p-3 rounded-full">
              <Globe className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-1">
                Share Your Creativity
              </h3>
              <p className="text-sm text-gray-600">
                Make your designs public and inspire others in our community gallery
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Button variant="outline" size="sm" className="flex items-center space-x-2">
                <Eye className="h-4 w-4" />
                <span>View Gallery</span>
              </Button>
            </Link>
            <Button size="sm" className="bg-purple-600 hover:bg-purple-700 flex items-center space-x-2">
              <span>Learn More</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
