"use client";

import { fabric } from "fabric";
import debounce from "lodash.debounce";
import { useCallback, useEffect, useRef, useState } from "react";

import { ResponseType } from "@/features/projects/api/use-get-project";
import { useUpdateProject } from "@/features/projects/api/use-update-project";
import { useThumbnailGenerator } from "@/features/editor/hooks/use-thumbnail-generator";

import { 
  ActiveTool, 
  selectionDependentTools
} from "@/features/editor/types";
import { Navbar } from "@/features/editor/components/navbar";
import { Footer } from "@/features/editor/components/footer";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Sidebar } from "@/features/editor/components/sidebar";
import { Toolbar } from "@/features/editor/components/toolbar";
import { ShapeSidebar } from "@/features/editor/components/shape-sidebar";
import { FillColorSidebar } from "@/features/editor/components/fill-color-sidebar";
import { StrokeColorSidebar } from "@/features/editor/components/stroke-color-sidebar";
import { StrokeWidthSidebar } from "@/features/editor/components/stroke-width-sidebar";
import { OpacitySidebar } from "@/features/editor/components/opacity-sidebar";
import { TextSidebar } from "@/features/editor/components/text-sidebar";
import { FontSidebar } from "@/features/editor/components/font-sidebar";
import { ImageSidebar } from "@/features/editor/components/image-sidebar";
import { FilterSidebar } from "@/features/editor/components/filter-sidebar";
import { DrawSidebar } from "@/features/editor/components/draw-sidebar";
import { AiSidebar } from "@/features/editor/components/ai-sidebar";
import { TemplateSidebar } from "@/features/editor/components/template-sidebar";
import { RemoveBgSidebar } from "@/features/editor/components/remove-bg-sidebar";
import { SettingsSidebar } from "@/features/editor/components/settings-sidebar";
import { TemplateConfigSidebar } from "@/features/editor/components/template-config-sidebar";

interface EditorProps {
  initialData: ResponseType["data"];
  initialActiveTool?: ActiveTool;
};

export const Editor = ({ initialData, initialActiveTool = "select" }: EditorProps) => {
  const { mutate } = useUpdateProject(initialData.id);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSave = useCallback(
    debounce(
      (values: {
        json: string,
        height: number,
        width: number,
      }) => {
        mutate(values);
    },
    5000 // Increased to 5 seconds for better performance
  ), [mutate]);

  const [activeTool, setActiveTool] = useState<ActiveTool>(initialActiveTool);

  const onClearSelection = useCallback(() => {
    if (selectionDependentTools.includes(activeTool)) {
      setActiveTool("select");
    }
  }, [activeTool]);

  const { init, editor } = useEditor({
    defaultState: initialData.json,
    defaultWidth: initialData.width,
    defaultHeight: initialData.height,
    clearSelectionCallback: onClearSelection,
    saveCallback: debouncedSave,
  });

  const handleManualSave = () => {
    if (editor?.canvas) {
      const workspace = editor.canvas
        .getObjects()
        .find((object) => object.name === "clip");
      const height = workspace?.height || initialData.height;
      const width = workspace?.width || initialData.width;
      const json = JSON.stringify(editor.canvas.toJSON());

      mutate({ json, height, width });
    }
  };

  // Generate thumbnails automatically when the canvas changes
  const { debouncedGenerateThumbnail, forceRegenerateThumbnail } = useThumbnailGenerator({
    editor,
    projectId: initialData.id,
  });

  // Trigger thumbnail generation when canvas changes
  useEffect(() => {
    if (editor?.canvas) {
      const handleCanvasChange = () => {
        debouncedGenerateThumbnail();
      };

      const canvas = editor.canvas;
      canvas.on('object:added', handleCanvasChange);
      canvas.on('object:removed', handleCanvasChange);
      canvas.on('object:modified', handleCanvasChange);
      canvas.on('path:created', handleCanvasChange);

      // Generate initial thumbnail when editor is ready
      const generateInitialThumbnail = () => {
        setTimeout(() => {
          console.log("Generating initial thumbnail...");
          debouncedGenerateThumbnail();
        }, 1000); // Wait a bit for canvas to be fully ready

        // Also generate one immediately for testing
        setTimeout(() => {
          console.log("Force generating thumbnail for testing...");
          forceRegenerateThumbnail();
        }, 3000);
      };

      generateInitialThumbnail();

      return () => {
        canvas.off('object:added', handleCanvasChange);
        canvas.off('object:removed', handleCanvasChange);
        canvas.off('object:modified', handleCanvasChange);
        canvas.off('path:created', handleCanvasChange);
      };
    }
  }, [editor, debouncedGenerateThumbnail]);

  // Trigger thumbnail generation when editor changes
  useEffect(() => {
    if (editor) {
      debouncedGenerateThumbnail();
    }
  }, [editor, debouncedGenerateThumbnail]);

  const onChangeActiveTool = useCallback((tool: ActiveTool) => {
    if (tool === "draw") {
      editor?.enableDrawingMode();
    }

    if (activeTool === "draw") {
      editor?.disableDrawingMode();
    }

    if (tool === activeTool) {
      return setActiveTool("select");
    }
    
    setActiveTool(tool);
  }, [activeTool, editor]);

  const canvasRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initializeCanvas = () => {
      // Ensure container is available and has dimensions
      if (!containerRef.current) return false;

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      // Don't initialize if container has no dimensions
      if (containerWidth === 0 || containerHeight === 0) {
        console.warn("Container has zero dimensions, delaying canvas initialization");
        return false;
      }

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    // Try to initialize immediately
    const canvas = initializeCanvas();

    if (!canvas) {
      // If initialization failed, retry after a short delay
      const timeoutId = setTimeout(() => {
        const retryCanvas = initializeCanvas();
        if (!retryCanvas) {
          console.error("Failed to initialize canvas after retry");
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }

    return () => {
      canvas.dispose();
    };
  }, [init]);

  return (
    <div className="h-full flex flex-col">
      <Navbar
        id={initialData.id}
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={onChangeActiveTool}
        onSave={handleManualSave}
        initialData={{
          name: initialData.name,
          isCustomizable: initialData.isCustomizable || false,
        }}
      />
      <div className="absolute h-[calc(100%-68px)] w-full top-[68px] flex">
        <Sidebar
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ShapeSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FillColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeWidthSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <OpacitySidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TextSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FontSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ImageSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FilterSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <RemoveBgSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <DrawSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <SettingsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateConfigSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
          projectId={initialData.id}
          initialData={{
            isCustomizable: initialData.isCustomizable || false,
            editableLayers: initialData.editableLayers,
          }}
        />
        <main className="bg-muted flex-1 overflow-auto relative flex flex-col">
          <Toolbar
            editor={editor}
            activeTool={activeTool}
            onChangeActiveTool={onChangeActiveTool}
            key={JSON.stringify(editor?.canvas.getActiveObject())}
          />
          <div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
            <canvas ref={canvasRef} />
          </div>
          <Footer editor={editor} />
        </main>
      </div>
    </div>
  );
};
