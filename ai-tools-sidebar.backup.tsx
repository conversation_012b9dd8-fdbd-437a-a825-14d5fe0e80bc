import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import AiPanel from "@/components/editor/panels/AiPanel";

import { cn } from "@/lib/utils";

interface AiToolsSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

export const AiToolsSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: AiToolsSidebarProps) => {
  const onClose = () => {
    onChangeActiveTool("select");
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "ai-tools" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="AI Tools"
        description="Generate, edit, and enhance images with AI"
      />
      
      <div className="flex-1 overflow-hidden">
        <AiPanel
          canvas={editor?.canvas}
          selectedObject={editor?.selectedObjects[0]}
          onClose={onClose}
        />
      </div>
      
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
