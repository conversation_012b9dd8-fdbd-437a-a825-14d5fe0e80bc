import { fabric } from "fabric";
import { useEffect, useRef } from "react";

interface UseCanvasEventsProps {
  save: () => void;
  canvas: fabric.Canvas | null;
  setSelectedObjects: (objects: fabric.Object[]) => void;
  clearSelectionCallback?: () => void;
};

export const useCanvasEvents = ({
  save,
  canvas,
  setSelectedObjects,
  clearSelectionCallback,
}: UseCanvasEventsProps) => {
  const isModifying = useRef(false);

  useEffect(() => {
    if (canvas) {
      // Save on object added/removed (these are significant changes)
      canvas.on("object:added", () => save());
      canvas.on("object:removed", () => save());

      // For modifications, only save when the modification is complete
      canvas.on("object:moving", () => {
        isModifying.current = true;
      });

      canvas.on("object:scaling", () => {
        isModifying.current = true;
      });

      canvas.on("object:rotating", () => {
        isModifying.current = true;
      });

      canvas.on("object:modified", () => {
        // Only save if we were actually modifying (not just selecting)
        if (isModifying.current) {
          save();
          isModifying.current = false;
        }
      });

      canvas.on("selection:created", (e) => {
        setSelectedObjects(e.selected || []);
      });
      canvas.on("selection:updated", (e) => {
        setSelectedObjects(e.selected || []);
      });
      canvas.on("selection:cleared", () => {
        setSelectedObjects([]);
        clearSelectionCallback?.();
      });
    }

    return () => {
      if (canvas) {
        canvas.off("object:added");
        canvas.off("object:removed");
        canvas.off("object:modified");
        canvas.off("object:moving");
        canvas.off("object:scaling");
        canvas.off("object:rotating");
        canvas.off("selection:created");
        canvas.off("selection:updated");
        canvas.off("selection:cleared");
      }
    };
  },
  [
    save,
    canvas,
    clearSelectionCallback,
    setSelectedObjects // No need for this, this is from setState
  ]);
};
