// Unit tests for AI Service
import { 
  generateImage, 
  removeBackground, 
  upscaleImage, 
  isApiConfigured,
  getAvailableModels,
  getStyleFilters,
  sanitizePrompt,
  AI_PROVIDERS 
} from '../src/services/aiService'

// Mock environment variables
const mockEnv = {
  TOGETHER_API_KEY: 'test-together-key',
  FAL_KEY: 'test-fal-key',
  REPLICATE_API_TOKEN: 'test-replicate-token'
}

// Mock external dependencies
jest.mock('replicate', () => {
  return jest.fn().mockImplementation(() => ({
    run: jest.fn().mockResolvedValue(['https://example.com/generated-image.png'])
  }))
})

jest.mock('@fal-ai/client', () => ({
  fal: {
    config: jest.fn(),
    subscribe: jest.fn().mockResolvedValue({
      images: [{ url: 'https://example.com/fal-image.png' }]
    })
  }
}))

jest.mock('together-ai', () => {
  return jest.fn().mockImplementation(() => ({
    images: {
      create: jest.fn().mockResolvedValue({
        data: [{ url: 'https://example.com/together-image.png' }]
      })
    }
  }))
})

describe('AI Service', () => {
  beforeEach(() => {
    // Reset environment variables
    Object.keys(mockEnv).forEach(key => {
      process.env[key] = mockEnv[key]
    })
    
    // Clear all mocks
    jest.clearAllMocks()
  })

  afterEach(() => {
    // Clean up environment variables
    Object.keys(mockEnv).forEach(key => {
      delete process.env[key]
    })
  })

  describe('isApiConfigured', () => {
    test('should detect configured APIs', () => {
      expect(isApiConfigured(AI_PROVIDERS.TOGETHER)).toBe(true)
      expect(isApiConfigured(AI_PROVIDERS.FAL)).toBe(true)
      expect(isApiConfigured(AI_PROVIDERS.REPLICATE)).toBe(true)
      expect(isApiConfigured()).toBe(true)
    })

    test('should detect unconfigured APIs', () => {
      delete process.env.TOGETHER_API_KEY
      delete process.env.FAL_KEY
      delete process.env.REPLICATE_API_TOKEN

      expect(isApiConfigured(AI_PROVIDERS.TOGETHER)).toBe(false)
      expect(isApiConfigured(AI_PROVIDERS.FAL)).toBe(false)
      expect(isApiConfigured(AI_PROVIDERS.REPLICATE)).toBe(false)
      expect(isApiConfigured()).toBe(false)
    })

    test('should not accept placeholder values', () => {
      process.env.TOGETHER_API_KEY = 'your-together-api-key-here'
      process.env.FAL_KEY = 'your-fal-api-key-here'
      process.env.REPLICATE_API_TOKEN = 'your-replicate-api-token-here'

      expect(isApiConfigured(AI_PROVIDERS.TOGETHER)).toBe(false)
      expect(isApiConfigured(AI_PROVIDERS.FAL)).toBe(false)
      expect(isApiConfigured(AI_PROVIDERS.REPLICATE)).toBe(false)
    })
  })

  describe('getAvailableModels', () => {
    test('should return available models when APIs are configured', () => {
      const models = getAvailableModels('text-to-image')
      expect(Array.isArray(models)).toBe(true)
      expect(models.length).toBeGreaterThan(0)
      expect(models[0]).toHaveProperty('id')
      expect(models[0]).toHaveProperty('name')
      expect(models[0]).toHaveProperty('provider')
    })

    test('should filter models by category', () => {
      const textToImageModels = getAvailableModels('text-to-image')
      const imageProcessingModels = getAvailableModels('image-processing')
      
      expect(textToImageModels.every(model => model.category === 'text-to-image')).toBe(true)
      expect(imageProcessingModels.every(model => model.category === 'image-processing')).toBe(true)
    })
  })

  describe('getStyleFilters', () => {
    test('should return available style filters', () => {
      const filters = getStyleFilters()
      expect(Array.isArray(filters)).toBe(true)
      expect(filters.length).toBeGreaterThan(0)
      expect(filters[0]).toHaveProperty('id')
      expect(filters[0]).toHaveProperty('name')
      expect(filters[0]).toHaveProperty('prompt')
      expect(filters[0]).toHaveProperty('icon')
    })
  })

  describe('sanitizePrompt', () => {
    test('should remove script tags', () => {
      const maliciousPrompt = 'A cat <script>alert("xss")</script> in a garden'
      const sanitized = sanitizePrompt(maliciousPrompt)
      expect(sanitized).toBe('A cat  in a garden')
    })

    test('should remove javascript: URLs', () => {
      const maliciousPrompt = 'A cat javascript:alert("xss") in a garden'
      const sanitized = sanitizePrompt(maliciousPrompt)
      expect(sanitized).toBe('A cat  in a garden')
    })

    test('should remove event handlers', () => {
      const maliciousPrompt = 'A cat onclick="alert()" in a garden'
      const sanitized = sanitizePrompt(maliciousPrompt)
      expect(sanitized).toBe('A cat  in a garden')
    })

    test('should limit prompt length', () => {
      const longPrompt = 'A'.repeat(2000)
      const sanitized = sanitizePrompt(longPrompt)
      expect(sanitized.length).toBe(1000)
    })

    test('should trim whitespace', () => {
      const prompt = '  A cat in a garden  '
      const sanitized = sanitizePrompt(prompt)
      expect(sanitized).toBe('A cat in a garden')
    })
  })

  describe('generateImage', () => {
    test('should generate image with valid prompt', async () => {
      const result = await generateImage('A beautiful sunset')
      expect(typeof result).toBe('string')
      expect(result).toMatch(/^https?:\/\//)
    })

    test('should handle custom options', async () => {
      const options = {
        width: 512,
        height: 512,
        steps: 20,
        guidance: 8.0
      }
      
      const result = await generateImage('A beautiful sunset', options)
      expect(typeof result).toBe('string')
      expect(result).toMatch(/^https?:\/\//)
    })

    test('should sanitize prompt', async () => {
      const maliciousPrompt = 'A cat <script>alert("xss")</script>'
      const result = await generateImage(maliciousPrompt)
      expect(typeof result).toBe('string')
    })

    test('should throw error when no providers configured', async () => {
      delete process.env.TOGETHER_API_KEY
      delete process.env.FAL_KEY
      delete process.env.REPLICATE_API_TOKEN

      await expect(generateImage('A cat')).rejects.toThrow('No AI providers configured')
    })
  })

  describe('removeBackground', () => {
    test('should remove background from image', async () => {
      const imageUrl = 'https://example.com/test-image.jpg'
      const result = await removeBackground(imageUrl)
      expect(typeof result).toBe('string')
      expect(result).toMatch(/^https?:\/\//)
    })

    test('should throw error for missing image URL', async () => {
      await expect(removeBackground('')).rejects.toThrow('Image URL is required')
    })

    test('should use specified provider', async () => {
      const imageUrl = 'https://example.com/test-image.jpg'
      const result = await removeBackground(imageUrl, AI_PROVIDERS.FAL)
      expect(typeof result).toBe('string')
    })
  })

  describe('upscaleImage', () => {
    test('should upscale image', async () => {
      const imageUrl = 'https://example.com/test-image.jpg'
      const result = await upscaleImage(imageUrl, 2)
      expect(typeof result).toBe('string')
      expect(result).toMatch(/^https?:\/\//)
    })

    test('should throw error for invalid scale', async () => {
      const imageUrl = 'https://example.com/test-image.jpg'
      await expect(upscaleImage(imageUrl, 0)).rejects.toThrow('Scale must be between 1 and 4')
      await expect(upscaleImage(imageUrl, 5)).rejects.toThrow('Scale must be between 1 and 4')
    })

    test('should throw error for missing image URL', async () => {
      await expect(upscaleImage('')).rejects.toThrow('Image URL is required')
    })
  })
})
