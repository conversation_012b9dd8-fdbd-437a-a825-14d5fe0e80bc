// Integration tests for AI Panel component
import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import AiPanel from '../../src/components/editor/panels/AiPanel'

// Mock the AI service
jest.mock('../../src/services/aiService', () => ({
  isApiConfigured: jest.fn(() => true),
  getAvailableModels: jest.fn(() => [
    {
      id: 'test-model',
      name: 'Test Model',
      provider: 'together',
      category: 'text-to-image'
    }
  ]),
  getStyleFilters: jest.fn(() => [
    {
      id: 'ARTISTIC',
      name: 'Artistic',
      prompt: 'artistic style',
      icon: '🎨'
    }
  ]),
  AI_PROVIDERS: {
    TOGETHER: 'together',
    FAL: 'fal',
    REPLICATE: 'replicate'
  }
}))

// Mock the client AI service
jest.mock('../../src/services/clientAiService', () => ({
  isClientSideAiAvailable: jest.fn(() => true),
  isClientSideBackgroundRemovalAvailable: jest.fn(() => true),
  getClientSideCapabilities: jest.fn(() => ({
    available: true,
    backgroundRemoval: true,
    upscaling: true,
    filters: true,
    resize: true,
    webgl2: true,
    offscreenCanvas: true,
    transformers: false
  }))
}))

// Mock the fabric utils
jest.mock('../../src/fabric/fabric-utils', () => ({
  addImageToCanvas: jest.fn().mockResolvedValue({}),
  getSelectedImageUrl: jest.fn(() => 'https://example.com/test-image.jpg'),
  applyProcessedImageToSelected: jest.fn().mockResolvedValue({})
}))

// Mock the API hooks
jest.mock('../../src/features/ai/api/use-generate-image', () => ({
  useGenerateImage: () => ({
    mutateAsync: jest.fn().mockResolvedValue({
      data: 'https://example.com/generated-image.png'
    }),
    isPending: false
  })
}))

jest.mock('../../src/features/ai/api/use-remove-bg', () => ({
  useRemoveBg: () => ({
    mutateAsync: jest.fn().mockResolvedValue({
      data: 'https://example.com/processed-image.png'
    }),
    isPending: false
  })
}))

// Mock canvas object
const mockCanvas = {
  add: jest.fn(),
  setActiveObject: jest.fn(),
  renderAll: jest.fn(),
  getActiveObject: jest.fn(() => ({
    type: 'image',
    _originalElement: {
      currentSrc: 'https://example.com/test-image.jpg'
    }
  }))
}

// Mock selected object
const mockSelectedObject = {
  type: 'image',
  _originalElement: {
    currentSrc: 'https://example.com/test-image.jpg'
  }
}

// Test wrapper with QueryClient
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('AI Panel Integration', () => {
  const defaultProps = {
    canvas: mockCanvas,
    selectedObject: null,
    onClose: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render all tabs', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    expect(screen.getByText('Generate')).toBeInTheDocument()
    expect(screen.getByText('Edit')).toBeInTheDocument()
    expect(screen.getByText('Enhance')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
  })

  test('should render generate tab by default', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    expect(screen.getByText('Generate Image')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/describe the image/i)).toBeInTheDocument()
  })

  test('should switch between tabs', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    // Click on Edit tab
    fireEvent.click(screen.getByText('Edit'))
    expect(screen.getByText('Edit Image')).toBeInTheDocument()

    // Click on Settings tab
    fireEvent.click(screen.getByText('Settings'))
    expect(screen.getByText('AI Provider Status')).toBeInTheDocument()
  })

  test('should handle prompt input', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    const promptInput = screen.getByPlaceholderText(/describe the image/i)
    fireEvent.change(promptInput, { target: { value: 'A beautiful sunset' } })
    
    expect(promptInput.value).toBe('A beautiful sunset')
  })

  test('should handle generation options', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    const widthInput = screen.getByDisplayValue('1024')
    fireEvent.change(widthInput, { target: { value: '512' } })
    
    expect(widthInput.value).toBe('512')
  })

  test('should show edit options when image is selected', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} selectedObject={mockSelectedObject} />
      </TestWrapper>
    )

    // Switch to Edit tab
    fireEvent.click(screen.getByText('Edit'))
    
    expect(screen.getByText('Remove Background')).toBeInTheDocument()
  })

  test('should show message when no image is selected in edit tab', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    // Switch to Edit tab
    fireEvent.click(screen.getByText('Edit'))
    
    expect(screen.getByText('Select an image to edit')).toBeInTheDocument()
  })

  test('should show API status in settings tab', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    // Switch to Settings tab
    fireEvent.click(screen.getByText('Settings'))
    
    expect(screen.getByText('Together AI')).toBeInTheDocument()
    expect(screen.getByText('Fal.ai')).toBeInTheDocument()
    expect(screen.getByText('Replicate')).toBeInTheDocument()
  })

  test('should show client-side capabilities in settings', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    // Switch to Settings tab
    fireEvent.click(screen.getByText('Settings'))
    
    expect(screen.getByText('Client-side Capabilities')).toBeInTheDocument()
    expect(screen.getByText('WebGL2 Support')).toBeInTheDocument()
    expect(screen.getByText('Offline Background Removal')).toBeInTheDocument()
  })

  test('should handle close button', () => {
    const onClose = jest.fn()
    
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} onClose={onClose} />
      </TestWrapper>
    )

    fireEvent.click(screen.getByText('×'))
    expect(onClose).toHaveBeenCalled()
  })

  test('should disable generate button when no prompt', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    const generateButton = screen.getByText('Generate Image')
    expect(generateButton).toBeDisabled()
  })

  test('should enable generate button with valid prompt', () => {
    render(
      <TestWrapper>
        <AiPanel {...defaultProps} />
      </TestWrapper>
    )

    const promptInput = screen.getByPlaceholderText(/describe the image/i)
    fireEvent.change(promptInput, { target: { value: 'A beautiful sunset' } })
    
    const generateButton = screen.getByText('Generate Image')
    expect(generateButton).not.toBeDisabled()
  })
})
