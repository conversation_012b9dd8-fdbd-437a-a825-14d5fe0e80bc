# Template Editor System Documentation

## Overview

This document provides a comprehensive technical reference for the template editor system in this Canva-clone codebase. The editor is built using React, TypeScript, and Fabric.js for canvas manipulation. It supports loading templates from JSON, interactive object manipulation, auto-zoom functionality, and real-time saving.

## Architecture Overview

### Component Hierarchy

```
Editor (main component)
├── Navbar (top navigation)
├── Sidebar components (tools and properties)
├── Toolbar (object manipulation tools)
├── Canvas container (main editing area)
└── Footer (status and controls)
```

### Core Hook System

The editor uses a modular hook architecture:

- `useEditor` - Main editor hook that orchestrates all functionality
- `useAutoResize` - Handles canvas sizing and auto-zoom
- `useCanvasEvents` - Manages object selection and interaction events
- `useLoadState` - Handles template loading from JSON
- `useHistory` - Undo/redo functionality with state management
- `useClipboard` - Copy/paste operations
- `useHotkeys` - Keyboard shortcuts

## Canvas Initialization Process

### 1. Container Setup

The editor initializes with a container reference and canvas element:

```typescript
const canvasRef = useRef(null);
const containerRef = useRef<HTMLDivElement>(null);

useEffect(() => {
  const initializeCanvas = () => {
    if (!containerRef.current) return false;
    
    const container = containerRef.current;
    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    
    // Validate container dimensions
    if (containerWidth === 0 || containerHeight === 0) {
      console.warn("Container has zero dimensions, delaying canvas initialization");
      return false;
    }
    
    const canvas = new fabric.Canvas(canvasRef.current, {
      controlsAboveOverlay: true,
      preserveObjectStacking: true,
    });
    
    init({ initialCanvas: canvas, initialContainer: container });
    return canvas;
  };
  
  // Try immediate initialization with retry fallback
  const canvas = initializeCanvas();
  if (!canvas) {
    const timeoutId = setTimeout(() => {
      const retryCanvas = initializeCanvas();
      if (!retryCanvas) {
        console.error("Failed to initialize canvas after retry");
      }
    }, 100);
    return () => clearTimeout(timeoutId);
  }
  
  return () => canvas.dispose();
}, [init]);
```

### 2. Workspace Creation

The editor creates a "workspace" object that defines the design area:

```typescript
const initialWorkspace = new fabric.Rect({
  width: workspaceWidth,
  height: workspaceHeight,
  name: "clip", // Important: used to identify workspace
  fill: "white",
  selectable: false,
  hasControls: false,
  shadow: new fabric.Shadow({
    color: "rgba(0,0,0,0.8)",
    blur: 5,
  }),
});

canvas.add(initialWorkspace);
canvas.centerObject(initialWorkspace);
canvas.clipPath = initialWorkspace; // Clips content to workspace bounds
```

### 3. Object Styling Configuration

Global object styling is configured during initialization:

```typescript
fabric.Object.prototype.set({
  cornerColor: "#FFF",
  cornerStyle: "circle",
  borderColor: "#3b82f6",
  borderScaleFactor: 1.5,
  transparentCorners: false,
  borderOpacityWhenMoving: 1,
  cornerStrokeColor: "#3b82f6",
});

// Fix for text baseline issues
fabric.Text.prototype.set({ textBaseline: "middle" });
fabric.Textbox.prototype.set({ textBaseline: "middle" });
```

## Template Loading and JSON State Management

### JSON Serialization Keys

The editor uses specific keys for serialization to preserve custom properties:

```typescript
export const JSON_KEYS = [
  "name",
  "gradientAngle", 
  "selectable",
  "hasControls",
  "linkData",
  "editable",
  "extensionType",
  "extension"
];
```

### Loading Process

Template loading is handled by the `useLoadState` hook:

```typescript
useEffect(() => {
  if (!initialized.current && initialState?.current && canvas) {
    try {
      const data = JSON.parse(initialState.current);
      
      // Validate canvas dimensions
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();
      
      if (canvasWidth <= 0 || canvasHeight <= 0) {
        console.warn("Canvas has invalid dimensions, skipping loadFromJSON");
        return;
      }
      
      canvas.loadFromJSON(data, () => {
        // Post-load validation and fixes
        canvas.getObjects().forEach(obj => {
          // Fix zero dimensions
          if (obj.width === 0 || obj.height === 0) {
            if (obj.width === 0) obj.set('width', 1);
            if (obj.height === 0) obj.set('height', 1);
          }
          
          // Fix invalid textBaseline values
          if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
            const textObj = obj as fabric.Text;
            const currentBaseline = (textObj as any).textBaseline;
            if (currentBaseline && !['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'].includes(currentBaseline)) {
              (textObj as any).textBaseline = 'middle';
            }
          }
        });
        
        // Initialize history and trigger auto-zoom
        const currentState = JSON.stringify(canvas.toJSON(JSON_KEYS));
        canvasHistory.current = [currentState];
        setHistoryIndex(0);
        autoZoom();
      });
      
      initialized.current = true;
    } catch (error) {
      console.error("Error loading canvas state:", error);
      initialized.current = true; // Prevent infinite retries
    }
  }
}, [canvas, autoZoom, initialState, canvasHistory, setHistoryIndex]);
```

## Auto-Zoom and Canvas Sizing

### Auto-Resize Hook

The `useAutoResize` hook manages canvas sizing and zoom functionality:

```typescript
const autoZoom = useCallback(() => {
  if (!canvas || !container) return;
  
  const width = container.offsetWidth;
  const height = container.offsetHeight;
  
  // Validate dimensions
  if (width <= 0 || height <= 0) {
    console.warn("Container has invalid dimensions for autoZoom");
    return;
  }
  
  const center = canvas.getCenter();
  const zoomRatio = 0.85;
  const localWorkspace = canvas.getObjects().find((object) => object.name === "clip");
  
  if (!localWorkspace) return;
  
  // Calculate optimal zoom scale
  const scale = fabric.util.findScaleToFit(localWorkspace, { width, height });
  const zoom = zoomRatio * scale;
  
  // Apply zoom and center workspace
  canvas.setViewportTransform(fabric.iMatrix.concat());
  canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);
  
  const workspaceCenter = localWorkspace.getCenterPoint();
  const viewportTransform = canvas.viewportTransform;
  
  if (canvas.width && canvas.height && viewportTransform) {
    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];
    canvas.setViewportTransform(viewportTransform);
  }
  
  // Update clip path
  localWorkspace.clone((cloned: fabric.Rect) => {
    canvas.clipPath = cloned;
    canvas.requestRenderAll();
  });
}, [canvas, container]);

// ResizeObserver for responsive behavior
useEffect(() => {
  let resizeObserver: ResizeObserver | null = null;
  
  if (canvas && container) {
    resizeObserver = new ResizeObserver(() => autoZoom());
    resizeObserver.observe(container);
  }
  
  return () => resizeObserver?.disconnect();
}, [canvas, container, autoZoom]);
```

## Object Management and Interaction

### Selection Handling

Object selection is managed through canvas events:

```typescript
canvas.on("selection:created", (e) => {
  setSelectedObjects(e.selected || []);
});

canvas.on("selection:updated", (e) => {
  setSelectedObjects(e.selected || []);
});

canvas.on("selection:cleared", () => {
  setSelectedObjects([]);
  clearSelectionCallback?.();
});
```

### Object Manipulation Events

The editor tracks object modifications for saving and history:

```typescript
const isModifying = useRef(false);

// Track modification start
canvas.on("object:moving", () => { isModifying.current = true; });
canvas.on("object:scaling", () => { isModifying.current = true; });
canvas.on("object:rotating", () => { isModifying.current = true; });

// Save only when modification completes
canvas.on("object:modified", () => {
  if (isModifying.current) {
    save();
    isModifying.current = false;
  }
});

// Immediate save for add/remove operations
canvas.on("object:added", () => save());
canvas.on("object:removed", () => save());
```

### Object Creation and Positioning

New objects are automatically centered in the workspace:

```typescript
const getWorkspace = () => {
  return canvas.getObjects().find((object) => object.name === "clip");
};

const center = (object: fabric.Object) => {
  const workspace = getWorkspace();
  const center = workspace?.getCenterPoint();
  if (!center) return;
  canvas._centerObject(object, center);
};

const addToCanvas = (object: fabric.Object) => {
  center(object);
  canvas.add(object);
  canvas.setActiveObject(object);
};
```

## History and State Management

### Undo/Redo System

The history system maintains a stack of canvas states:

```typescript
const save = useCallback((skip = false) => {
  if (!canvas) return;
  
  const currentState = canvas.toJSON(JSON_KEYS);
  const json = JSON.stringify(currentState);
  
  if (!skip && !skipSave.current) {
    canvasHistory.current.push(json);
    setHistoryIndex(canvasHistory.current.length - 1);
  }
  
  // Extract workspace dimensions for external saving
  const workspace = canvas.getObjects().find((object) => object.name === "clip");
  const height = workspace?.height || 0;
  const width = workspace?.width || 0;
  
  saveCallback?.({ json, height, width });
}, [canvas, saveCallback]);

const undo = useCallback(() => {
  if (canUndo()) {
    skipSave.current = true;
    canvas?.clear().renderAll();
    
    const previousIndex = historyIndex - 1;
    const previousState = JSON.parse(canvasHistory.current[previousIndex]);
    
    canvas?.loadFromJSON(previousState, () => {
      canvas.renderAll();
      setHistoryIndex(previousIndex);
      skipSave.current = false;
    });
  }
}, [canUndo, canvas, historyIndex]);
```

### Debounced Auto-Save

The editor implements debounced saving to prevent excessive API calls:

```typescript
const debouncedSave = useCallback(
  debounce((values: { json: string, height: number, width: number }) => {
    mutate(values);
  }, 5000), // 5 second debounce
  [mutate]
);
```

## Copy/Paste System

The clipboard system handles object duplication:

```typescript
const copy = useCallback(() => {
  canvas?.getActiveObject()?.clone((cloned: any) => {
    clipboard.current = cloned;
  });
}, [canvas]);

const paste = useCallback(() => {
  if (!clipboard.current) return;
  
  clipboard.current.clone((clonedObj: any) => {
    canvas?.discardActiveObject();
    clonedObj.set({
      left: clonedObj.left + 10,
      top: clonedObj.top + 10,
      evented: true,
    });
    
    if (clonedObj.type === "activeSelection") {
      // Handle multiple object selection
      clonedObj.canvas = canvas;
      clonedObj.forEachObject((obj: any) => canvas?.add(obj));
      clonedObj.setCoords();
    } else {
      canvas?.add(clonedObj);
    }
    
    // Offset for next paste
    clipboard.current.top += 10;
    clipboard.current.left += 10;
    canvas?.setActiveObject(clonedObj);
    canvas?.requestRenderAll();
  });
}, [canvas]);
```

## Common Problem Areas and Solutions

### 1. Canvas Initialization Timing

**Problem**: Canvas fails to initialize due to zero container dimensions.

**Solution**: Implement retry logic with dimension validation:

```typescript
if (containerWidth === 0 || containerHeight === 0) {
  console.warn("Container has zero dimensions, delaying canvas initialization");
  return false;
}
```

### 2. Text Baseline Issues

**Problem**: Invalid textBaseline values cause rendering errors.

**Solution**: Validate and fix textBaseline during loading:

```typescript
if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
  const validBaselines = ['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'];
  if (!validBaselines.includes(currentBaseline)) {
    (textObj as any).textBaseline = 'middle';
  }
}
```

### 3. Zero Dimension Objects

**Problem**: Objects with zero width/height cause rendering issues.

**Solution**: Set minimum dimensions during loading:

```typescript
if (obj.width === 0 || obj.height === 0) {
  if (obj.width === 0) obj.set('width', 1);
  if (obj.height === 0) obj.set('height', 1);
}
```

### 4. Auto-Zoom Timing

**Problem**: Auto-zoom called before canvas is ready.

**Solution**: Validate canvas state before zooming:

```typescript
if (canvasWidth <= 0 || canvasHeight <= 0) {
  console.warn("Canvas has invalid dimensions for autoZoom");
  return;
}
```

### 5. Memory Leaks

**Problem**: Event listeners and observers not properly cleaned up.

**Solution**: Implement proper cleanup in useEffect returns:

```typescript
return () => {
  if (canvas) {
    canvas.off("object:added");
    canvas.off("object:removed");
    // ... other event cleanup
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
};
```

## Key Integration Points

### Editor Hook Usage

```typescript
const { init, editor } = useEditor({
  defaultState: initialData.json,
  defaultWidth: initialData.width,
  defaultHeight: initialData.height,
  clearSelectionCallback: onClearSelection,
  saveCallback: debouncedSave,
});
```

### Canvas Container Structure

```jsx
<div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
  <canvas ref={canvasRef} />
</div>
```

### Event Handling Pattern

```typescript
useEffect(() => {
  if (editor) {
    const canvas = editor.canvas;
    canvas.on('object:added', handleCanvasChange);
    canvas.on('object:removed', handleCanvasChange);
    canvas.on('object:modified', handleCanvasChange);
    
    return () => {
      canvas.off('object:added', handleCanvasChange);
      canvas.off('object:removed', handleCanvasChange);
      canvas.off('object:modified', handleCanvasChange);
    };
  }
}, [editor]);
```

## Advanced Features and Patterns

### Keyboard Shortcuts System

The editor implements comprehensive keyboard shortcuts:

```typescript
useEvent("keydown", (event) => {
  const isCtrlKey = event.ctrlKey || event.metaKey;
  const isInput = ["INPUT", "TEXTAREA"].includes((event.target as HTMLElement).tagName);

  if (isInput) return; // Don't interfere with form inputs

  // Object deletion
  if (event.keyCode === 46) { // Delete key
    canvas?.getActiveObjects().forEach((object) => canvas?.remove(object));
    canvas?.discardActiveObject();
    canvas?.renderAll();
  }

  if (event.key === "Backspace") {
    canvas?.remove(...canvas.getActiveObjects());
    canvas?.discardActiveObject();
  }

  // Standard shortcuts
  if (isCtrlKey && event.key === "z") { event.preventDefault(); undo(); }
  if (isCtrlKey && event.key === "y") { event.preventDefault(); redo(); }
  if (isCtrlKey && event.key === "c") { event.preventDefault(); copy(); }
  if (isCtrlKey && event.key === "v") { event.preventDefault(); paste(); }
  if (isCtrlKey && event.key === "s") { event.preventDefault(); save(true); }

  // Select all
  if (isCtrlKey && event.key === "a") {
    event.preventDefault();
    canvas?.discardActiveObject();
    const allObjects = canvas?.getObjects().filter((object) => object.selectable);
    canvas?.setActiveObject(new fabric.ActiveSelection(allObjects, { canvas }));
    canvas?.renderAll();
  }
});
```

### Image Handling and Filters

The editor supports image manipulation with filters:

```typescript
changeImageFilter: (value: string) => {
  const objects = canvas.getActiveObjects();
  objects.forEach((object) => {
    if (object.type === "image") {
      const imageObject = object as fabric.Image;
      const effect = createFilter(value);
      imageObject.filters = effect ? [effect] : [];
      imageObject.applyFilters();
      canvas.renderAll();
    }
  });
},

addImage: (value: string) => {
  fabric.Image.fromURL(value, (image) => {
    const workspace = getWorkspace();
    image.scaleToWidth(workspace?.width || 0);
    image.scaleToHeight(workspace?.height || 0);
    addToCanvas(image);
  }, { crossOrigin: "anonymous" });
}
```

### Layer Management

Objects can be reordered with workspace preservation:

```typescript
bringForward: () => {
  canvas.getActiveObjects().forEach((object) => {
    canvas.bringForward(object);
  });
  canvas.renderAll();

  // Ensure workspace stays at bottom
  const workspace = getWorkspace();
  workspace?.sendToBack();
},

sendBackwards: () => {
  canvas.getActiveObjects().forEach((object) => {
    canvas.sendBackwards(object);
  });
  canvas.renderAll();

  // Ensure workspace stays at bottom
  const workspace = getWorkspace();
  workspace?.sendToBack();
}
```

### Text Object Management

Text objects require special handling for properties:

```typescript
changeFontSize: (value: number) => {
  canvas.getActiveObjects().forEach((object) => {
    if (isTextType(object.type)) {
      object.set({ fontSize: value });
    }
  });
  canvas.renderAll();
},

changeFontWeight: (value: number) => {
  canvas.getActiveObjects().forEach((object) => {
    if (isTextType(object.type)) {
      object.set({ fontWeight: value });
    }
  });
  canvas.renderAll();
}
```

## Troubleshooting Guide

### Canvas Not Rendering

**Symptoms**: Blank canvas, no objects visible
**Causes**:
- Container has zero dimensions
- Canvas initialization failed
- Invalid JSON data

**Debug Steps**:
1. Check container dimensions: `container.offsetWidth`, `container.offsetHeight`
2. Verify canvas creation: `console.log(canvas.getWidth(), canvas.getHeight())`
3. Validate JSON structure: `JSON.parse(jsonData)`

### Objects Not Selectable

**Symptoms**: Cannot select or interact with objects
**Causes**:
- Objects have `selectable: false`
- Objects are behind workspace
- Event listeners not properly attached

**Debug Steps**:
1. Check object properties: `object.selectable`, `object.evented`
2. Verify object layering: `canvas.getObjects().map(o => o.name)`
3. Test event binding: `canvas.getActiveObject()`

### Auto-Zoom Not Working

**Symptoms**: Canvas doesn't fit container, objects appear too small/large
**Causes**:
- Workspace object missing
- Invalid container dimensions
- Viewport transform issues

**Debug Steps**:
1. Find workspace: `canvas.getObjects().find(o => o.name === "clip")`
2. Check dimensions: `workspace.width`, `workspace.height`
3. Verify transform: `canvas.viewportTransform`

### Memory Leaks

**Symptoms**: Performance degradation over time
**Causes**:
- Event listeners not cleaned up
- Canvas not disposed
- ResizeObserver not disconnected

**Prevention**:
```typescript
useEffect(() => {
  // Setup code
  return () => {
    canvas?.dispose();
    resizeObserver?.disconnect();
    canvas?.off(); // Remove all listeners
  };
}, []);
```

### JSON Loading Failures

**Symptoms**: Template doesn't load, objects missing
**Causes**:
- Corrupted JSON data
- Missing custom properties
- Version incompatibility

**Solutions**:
1. Validate JSON structure before loading
2. Include all custom keys in `JSON_KEYS`
3. Implement fallback for missing properties

## Performance Optimization

### Rendering Optimization

```typescript
// Batch operations to reduce renders
canvas.renderOnAddRemove = false;
// ... add multiple objects
canvas.renderOnAddRemove = true;
canvas.renderAll();

// Use requestRenderAll for better performance
canvas.requestRenderAll();
```

### Event Debouncing

```typescript
// Debounce save operations
const debouncedSave = debounce(save, 1000);

// Throttle resize events
const throttledResize = throttle(autoZoom, 100);
```

### Memory Management

```typescript
// Dispose of unused objects
object.dispose?.();

// Clear canvas properly
canvas.clear();
canvas.dispose();

// Remove event listeners
canvas.off();
```

## Integration with External Systems

### API Integration

```typescript
// Save to backend
const handleSave = async () => {
  const workspace = editor.getWorkspace();
  const json = JSON.stringify(canvas.toJSON(JSON_KEYS));

  await api.updateProject({
    id: projectId,
    json,
    width: workspace?.width,
    height: workspace?.height
  });
};

// Load from backend
const handleLoad = async (projectId: string) => {
  const project = await api.getProject(projectId);
  editor.loadJson(project.json);
};
```

### Thumbnail Generation

```typescript
const generateThumbnail = () => {
  const workspace = canvas.getObjects().find(o => o.name === "clip");
  if (!workspace) return;

  // Reset viewport for clean export
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  const dataURL = canvas.toDataURL({
    format: 'png',
    quality: 1,
    multiplier: 2, // Higher resolution
    left: workspace.left,
    top: workspace.top,
    width: workspace.width,
    height: workspace.height
  });

  // Restore viewport
  autoZoom();

  return dataURL;
};
```

## Best Practices and Patterns

### Error Handling

Always implement robust error handling for canvas operations:

```typescript
const safeCanvasOperation = (operation: () => void) => {
  try {
    if (!canvas) {
      console.warn("Canvas not initialized");
      return;
    }
    operation();
  } catch (error) {
    console.error("Canvas operation failed:", error);
    // Optionally show user-friendly error message
  }
};
```

### State Validation

Validate canvas state before critical operations:

```typescript
const validateCanvasState = () => {
  if (!canvas) return false;
  if (canvas.getWidth() <= 0 || canvas.getHeight() <= 0) return false;

  const workspace = canvas.getObjects().find(o => o.name === "clip");
  if (!workspace) return false;

  return true;
};
```

### Custom Object Properties

Extend Fabric.js objects with custom properties:

```typescript
// Define custom properties in JSON_KEYS
export const JSON_KEYS = [
  "name", "editable", "editableName", "editablePlaceholder",
  "linkData", "extensionType", "extension"
];

// Set custom properties on objects
object.set({
  editable: true,
  editableName: "Header Text",
  editablePlaceholder: "Enter your text here"
});
```

### Workspace Management

Always maintain workspace integrity:

```typescript
const ensureWorkspaceIntegrity = () => {
  const workspace = canvas.getObjects().find(o => o.name === "clip");
  if (workspace) {
    workspace.set({
      selectable: false,
      hasControls: false,
      evented: false
    });
    workspace.sendToBack();
  }
};
```

### Event Listener Patterns

Use consistent patterns for event management:

```typescript
useEffect(() => {
  if (!canvas) return;

  const handlers = {
    'object:added': handleObjectAdded,
    'object:removed': handleObjectRemoved,
    'selection:created': handleSelectionCreated,
    'selection:cleared': handleSelectionCleared
  };

  // Attach listeners
  Object.entries(handlers).forEach(([event, handler]) => {
    canvas.on(event, handler);
  });

  // Cleanup
  return () => {
    Object.entries(handlers).forEach(([event, handler]) => {
      canvas.off(event, handler);
    });
  };
}, [canvas]);
```

## Common Implementation Patterns

### Loading States

Implement proper loading states for better UX:

```typescript
const [isCanvasLoading, setIsCanvasLoading] = useState(true);

useEffect(() => {
  if (editor && editor.canvas) {
    const checkCanvasReady = () => {
      const objects = editor.canvas.getObjects();
      if (objects.length > 1) { // More than just workspace
        setIsCanvasLoading(false);
        editor.autoZoom();
      } else {
        setTimeout(checkCanvasReady, 200);
      }
    };

    setTimeout(checkCanvasReady, 300);
  }
}, [editor]);
```

### Responsive Design

Handle responsive canvas behavior:

```typescript
useEffect(() => {
  const handleResize = () => {
    if (editor && containerRef.current) {
      const container = containerRef.current;
      const canvas = editor.canvas;

      canvas.setWidth(container.offsetWidth);
      canvas.setHeight(container.offsetHeight);
      editor.autoZoom();
    }
  };

  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, [editor]);
```

### Object Validation

Validate objects before operations:

```typescript
const isValidObject = (object: fabric.Object) => {
  return object &&
         object.width > 0 &&
         object.height > 0 &&
         object.name !== "clip";
};

const getValidSelectedObjects = () => {
  return canvas.getActiveObjects().filter(isValidObject);
};
```

## Testing Strategies

### Unit Testing Canvas Operations

```typescript
describe('Canvas Operations', () => {
  let canvas: fabric.Canvas;

  beforeEach(() => {
    canvas = new fabric.Canvas(null);
  });

  afterEach(() => {
    canvas.dispose();
  });

  test('should add object to canvas', () => {
    const rect = new fabric.Rect({ width: 100, height: 100 });
    canvas.add(rect);
    expect(canvas.getObjects()).toHaveLength(1);
  });
});
```

### Integration Testing

```typescript
test('should load template and auto-zoom', async () => {
  const { result } = renderHook(() => useEditor({
    defaultState: mockTemplateJson,
    defaultWidth: 800,
    defaultHeight: 600
  }));

  await waitFor(() => {
    expect(result.current.editor).toBeDefined();
  });

  expect(result.current.editor.canvas.getObjects().length).toBeGreaterThan(0);
});
```

## Migration and Compatibility

### Version Compatibility

Handle different template versions:

```typescript
const migrateTemplate = (templateData: any) => {
  // Handle version 1.0 templates
  if (!templateData.version || templateData.version === "1.0") {
    templateData.objects?.forEach((obj: any) => {
      if (obj.type === 'text' && !obj.textBaseline) {
        obj.textBaseline = 'middle';
      }
    });
    templateData.version = "2.0";
  }

  return templateData;
};
```

### Fabric.js Version Updates

When updating Fabric.js, test these critical areas:

1. Object serialization/deserialization
2. Text rendering and baseline behavior
3. Event handling changes
4. Performance implications
5. Breaking API changes

## Conclusion

This comprehensive documentation covers the essential aspects of the template editor system. Key takeaways for implementing a similar system:

1. **Robust Initialization**: Always validate container dimensions and implement retry logic
2. **Proper Event Management**: Use consistent patterns for attaching/detaching event listeners
3. **State Validation**: Validate canvas and object state before operations
4. **Error Handling**: Implement comprehensive error handling for all canvas operations
5. **Performance**: Use debouncing, throttling, and proper cleanup to maintain performance
6. **Testing**: Implement both unit and integration tests for critical functionality

The modular hook architecture makes the system maintainable and testable, while the comprehensive error handling ensures stability in production environments.
