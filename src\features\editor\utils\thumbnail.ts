import { fabric } from "fabric";
import { memoizeAdvanced, imageCache } from "@/lib/advanced-cache";
import { getCanvasOptimizer } from "@/lib/canvas-optimizer";

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: string;
  maintainAspectRatio?: boolean;
}

// Efficient thumbnail cache with content-based keys
const thumbnailCache = new Map<string, { dataUrl: string; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Generate content hash for cache key
function generateContentHash(canvas: fabric.Canvas): string {
  const objects = canvas.getObjects();
  const hashData = objects.map(obj => ({
    type: obj.type,
    left: Math.round(obj.left || 0),
    top: Math.round(obj.top || 0),
    scaleX: Math.round((obj.scaleX || 1) * 100) / 100,
    scaleY: Math.round((obj.scaleY || 1) * 100) / 100,
    angle: Math.round(obj.angle || 0),
  }));
  return JSON.stringify(hashData);
}



// Optimized thumbnail generation with intelligent caching
export const generateThumbnail = (
  canvas: fabric.Canvas,
  options: ThumbnailOptions = {}
): string => {
  const {
    width = 300,
    height = 200,
    quality = 0.7,
    format = "image/jpeg",
    maintainAspectRatio = true
  } = options;

  // Validate canvas
  if (!canvas) {
    console.error("Canvas is null or undefined");
    throw new Error("Canvas is required for thumbnail generation");
  }

  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  if (canvasWidth <= 0 || canvasHeight <= 0) {
    console.error("Canvas has invalid dimensions:", { canvasWidth, canvasHeight });
    throw new Error("Canvas has invalid dimensions");
  }

  // Generate cache key based on content and options
  const contentHash = generateContentHash(canvas);
  const optionsHash = JSON.stringify({ width, height, quality, format, maintainAspectRatio });
  const cacheKey = `${contentHash}-${optionsHash}`;

  // Check cache first
  const cached = thumbnailCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.dataUrl;
  }

  // Get the workspace (the main design area)
  const workspace = canvas.getObjects().find(
    (object) => object.name === "clip"
  ) as fabric.Rect;

  if (!workspace) {
    console.warn("No workspace found, using entire canvas");
    // Fallback: use entire canvas with ultra-high quality
    return canvas.toDataURL({
      format,
      quality,
      multiplier: 3, // Ultra-high resolution for crisp quality
    });
  }

  // Get workspace dimensions
  const workspaceWidth = workspace.width! * (workspace.scaleX || 1);
  const workspaceHeight = workspace.height! * (workspace.scaleY || 1);

  // Validate workspace dimensions
  if (workspaceWidth <= 0 || workspaceHeight <= 0) {
    console.error("Workspace has invalid dimensions:", { workspaceWidth, workspaceHeight });
    throw new Error("Workspace has invalid dimensions");
  }

  let thumbnailWidth = width;
  let thumbnailHeight = height;

  if (maintainAspectRatio) {
    // Calculate proper aspect ratio
    const aspectRatio = workspaceWidth / workspaceHeight;

    if (aspectRatio > 1) {
      // Landscape: fit to width
      thumbnailHeight = width / aspectRatio;
    } else {
      // Portrait: fit to height
      thumbnailWidth = height * aspectRatio;
    }
  }

  // Save current state
  const currentTransform = canvas.viewportTransform?.slice();
  const currentZoom = canvas.getZoom();
  const activeObject = canvas.getActiveObject();

  // Reset viewport to identity for clean rendering
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
  canvas.setZoom(1);

  // Temporarily hide any selection or controls for clean thumbnail
  canvas.discardActiveObject();
  canvas.renderAll();

  // Use canvas optimizer for efficient rendering
  const optimizer = getCanvasOptimizer(canvas);

  // Use reasonable multiplier for good quality but smaller file size
  const pixelRatio = window.devicePixelRatio || 1;
  const multiplier = Math.min(1.5, pixelRatio); // Reduced for better performance

  // Generate optimized thumbnail
  const dataUrl = optimizer.optimizedToDataURL({
    format,
    quality,
    left: workspace.left,
    top: workspace.top,
    width: workspaceWidth,
    height: workspaceHeight,
    multiplier: multiplier,
    enableRetinaScaling: false, // Disabled for performance
  });

  // Cache the result
  thumbnailCache.set(cacheKey, { dataUrl, timestamp: Date.now() });

  // Restore original state
  if (currentTransform) {
    canvas.setViewportTransform(currentTransform);
  }
  canvas.setZoom(currentZoom);
  if (activeObject) {
    canvas.setActiveObject(activeObject);
  }
  canvas.renderAll();

  return dataUrl;
};

export const uploadThumbnail = async (
  dataUrl: string,
  projectId: string
): Promise<string | null> => {
  try {
    // Convert data URL to blob
    const response = await fetch(dataUrl);
    const blob = await response.blob();

    // Create form data
    const formData = new FormData();
    formData.append("file", blob, `thumbnail-${projectId}.jpg`);

    // Upload using simple upload API
    const uploadResponse = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!uploadResponse.ok) {
      throw new Error("Failed to upload thumbnail");
    }

    const uploadResult = await uploadResponse.json();
    return uploadResult.url || null;
  } catch (error) {
    console.error("Error uploading thumbnail:", error);
    return null;
  }
};

export const generateAndUploadThumbnail = async (
  canvas: fabric.Canvas,
  projectId: string,
  options?: ThumbnailOptions
): Promise<string | null> => {
  try {
    const thumbnailDataUrl = generateThumbnail(canvas, options);
    const thumbnailUrl = await uploadThumbnail(thumbnailDataUrl, projectId);
    return thumbnailUrl;
  } catch (error) {
    console.error("Error generating and uploading thumbnail:", error);
    return null;
  }
};
