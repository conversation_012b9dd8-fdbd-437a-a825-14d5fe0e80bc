/**
 * Performance monitoring utilities for the Canva clone project
 * Helps track and optimize slow operations
 */

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 100; // Keep last 100 metrics

  /**
   * Start timing an operation
   */
  startTimer(operation: string): () => void {
    const startTime = performance.now();
    
    return (metadata?: Record<string, any>) => {
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration, metadata);
    };
  }

  /**
   * Record a performance metric
   */
  private recordMetric(operation: string, duration: number, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    // Log slow operations
    if (duration > 1000) { // More than 1 second
      console.warn(`🐌 Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`, metadata);
    } else if (duration > 500) { // More than 500ms
      console.log(`⚠️ Moderate operation: ${operation} took ${duration.toFixed(2)}ms`, metadata);
    }
  }

  /**
   * Get performance statistics
   */
  getStats(operation?: string): {
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    recentMetrics: PerformanceMetric[];
  } {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        recentMetrics: [],
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    
    return {
      count: filteredMetrics.length,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      recentMetrics: filteredMetrics.slice(-10), // Last 10 metrics
    };
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = [];
  }

  /**
   * Log performance summary
   */
  logSummary() {
    const operations = [...new Set(this.metrics.map(m => m.operation))];
    
    console.group('📊 Performance Summary');
    operations.forEach(operation => {
      const stats = this.getStats(operation);
      console.log(`${operation}: ${stats.count} calls, avg: ${stats.avgDuration.toFixed(2)}ms, max: ${stats.maxDuration.toFixed(2)}ms`);
    });
    console.groupEnd();
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for timing async functions
 */
export function timed(operation: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!;

    descriptor.value = async function (...args: any[]) {
      const endTimer = performanceMonitor.startTimer(operation);
      try {
        const result = await originalMethod.apply(this, args);
        endTimer({ success: true });
        return result;
      } catch (error) {
        endTimer({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
        throw error;
      }
    } as T;

    return descriptor;
  };
}

/**
 * Utility for timing code blocks
 */
export async function timeOperation<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const endTimer = performanceMonitor.startTimer(operation);
  try {
    const result = await fn();
    endTimer({ ...metadata, success: true });
    return result;
  } catch (error) {
    endTimer({ 
      ...metadata, 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
}

// Export for debugging in browser console
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor;
}
