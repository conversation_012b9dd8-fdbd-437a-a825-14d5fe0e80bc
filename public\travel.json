{"version": "5.3.0", "objects": [{"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "name": "clip", "selectable": false, "hasControls": false}, {"type": "image", "version": "5.3.0", "originX": "left", "originY": "top", "left": -156.31547396, "top": -290.49606594, "width": 1080, "height": 614, "fill": "rgb(0,0,0)", "stroke": null, "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.70156488, "scaleY": 1.95439739, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "cropX": 0, "cropY": 0, "selectable": true, "hasControls": true, "src": "https://images.unsplash.com/photo-1518950957614-73ac0a001408?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1MjE5OTh8MHwxfHJhbmRvbXx8fHx8fHx8fDE3MTk0MzM0Nzl8&ixlib=rb-4.0.3&q=80&w=1080", "crossOrigin": "anonymous", "filters": []}, {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 28.02322967, "top": -306.45710004, "width": 400, "height": 400, "fill": "rgba(0, 0, 0, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 2, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 3.075667, "scaleY": 3.075667, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.45, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": 281.43686774, "top": 135.79130761, "width": 300, "height": 300, "fill": "rgba(78, 145, 145, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 3.57267266, "scaleY": 3.57267266, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.72, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 498.50034799, "top": 314.0169935, "width": 234.88867188, "height": 50.85, "fill": "rgba(254, 254, 254, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.74624017, "scaleY": 1.74624017, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "Brush Script MT", "fontWeight": 500, "fontSize": 45, "text": "Its time to", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 432.66916448, "top": 420.68139255, "width": 271.12422089, "height": 50.85, "fill": "rgba(254, 254, 254, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 2.15484556, "scaleY": 2.15484556, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 500, "fontSize": 45, "text": "TRAVEL 🏝️", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 401.19670288, "top": 658.46712583, "width": 402.74732486, "height": 50.85, "fill": "rgba(254, 254, 254, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.60484426, "scaleY": 1.60484426, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "Brush Script MT", "fontWeight": 500, "fontSize": 45, "text": "Explore the island", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 586.96336093, "top": 754.39814037, "width": 234.88867188, "height": 50.85, "fill": "rgba(254, 254, 254, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.63590157, "scaleY": 1.63590157, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 500, "fontSize": 45, "text": "25% off", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": -513.12663919, "top": 559.66167104, "width": 300, "height": 300, "fill": "rgba(116, 211, 211, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.80344573, "scaleY": 1.80344573, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": -77.24053953, "top": -667.99662795, "width": 300, "height": 300, "fill": "rgba(78, 145, 145, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 2.71131385, "scaleY": 2.71131385, "angle": 0, "flipX": false, "flipY": false, "opacity": 0.72, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 230.75492307, "top": -176.28750966, "width": 234.88867188, "height": 109.836, "fill": "rgba(254, 254, 254, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.4971626, "scaleY": 1.4971626, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": 500, "fontSize": 45, "text": "4 SPOTS LEFT", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}], "clipPath": {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}}