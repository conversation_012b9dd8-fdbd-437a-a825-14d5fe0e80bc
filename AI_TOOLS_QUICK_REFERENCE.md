# AI Tools Quick Reference Guide

## Essential API Endpoints & Keys

### Together AI
- **Website**: https://together.ai
- **API Docs**: https://docs.together.ai/
- **Models**:
  - `black-forest-labs/FLUX.1-schnell` (Fast)
  - `black-forest-labs/FLUX.1-dev` (High Quality)
  - `black-forest-labs/FLUX.1-Kontext-Max` (Context-aware)
- **Environment Variables**:
  ```env
  TOGETHER_API_KEY="your-key-here"
  NEXT_PUBLIC_TOGETHER_API_KEY="your-key-here"
  ```

### Fal.ai
- **Website**: https://fal.ai
- **API Docs**: https://fal.ai/docs
- **Models**:
  - `fal-ai/flux-pro/v1.1` (Premium)
  - `fal-ai/flux/dev` (Development)
  - `fal-ai/birefnet` (Background Removal)
  - `fal-ai/clarity-upscaler` (Upscaling)
- **Environment Variables**:
  ```env
  FAL_KEY="your-key-here"
  NEXT_PUBLIC_FAL_KEY="your-key-here"
  ```

### Replicate
- **Website**: https://replicate.com
- **API Docs**: https://replicate.com/docs
- **Models**:
  - `black-forest-labs/flux-schnell` (Fast)
  - `black-forest-labs/flux-dev` (High Quality)
  - `cjwbw/rembg` (Background Removal)
  - `nightmareai/real-esrgan` (Upscaling)
- **Environment Variables**:
  ```env
  REPLICATE_API_TOKEN="your-token-here"
  NEXT_PUBLIC_REPLICATE_API_TOKEN="your-token-here"
  ```

## Quick Setup Commands

### 1. Install Dependencies
```bash
npm install @fal-ai/client @xenova/transformers fabric replicate together-ai @radix-ui/react-tabs @radix-ui/react-select lucide-react zustand
```

### 2. Create Environment File
```bash
cp .env.example .env.local
```

### 3. Add API Keys to .env.local
```env
# Copy your actual API keys here
TOGETHER_API_KEY="your-together-key"
NEXT_PUBLIC_TOGETHER_API_KEY="your-together-key"
FAL_KEY="your-fal-key"
NEXT_PUBLIC_FAL_KEY="your-fal-key"
REPLICATE_API_TOKEN="your-replicate-token"
NEXT_PUBLIC_REPLICATE_API_TOKEN="your-replicate-token"
```

### 4. Start Development Server
```bash
npm run dev
```

## Core File Structure
```
src/
├── services/
│   ├── aiService.js              # Main AI service
│   ├── clientAiService.js        # Browser fallbacks
│   └── aiImageService.js         # Image processing
├── components/
│   └── editor/
│       └── panels/
│           └── AiPanel.jsx       # Main AI UI
├── fabric/
│   └── fabric-utils.js           # Canvas integration
├── app/
│   └── api/
│       ├── proxy-image/
│       │   └── route.js          # Image proxy
│       └── ai/
│           └── remove-background/
│               └── route.js      # Background removal API
└── store/
    └── index.js                  # State management
```

## Essential Functions

### Generate Image
```javascript
import { generateImage } from '@/services/aiService';

const imageUrl = await generateImage('a beautiful sunset', {
  width: 1024,
  height: 1024,
  steps: 28
});
```

### Remove Background
```javascript
import { removeBackground } from '@/services/aiService';

const processedUrl = await removeBackground(imageUrl, 'fal');
```

### Upscale Image
```javascript
import { upscaleImage } from '@/services/aiService';

const upscaledUrl = await upscaleImage(imageUrl, 2, 'fal');
```

### Add Image to Canvas
```javascript
import { addImageToCanvas } from '@/fabric/fabric-utils';

const fabricImage = await addImageToCanvas(canvas, imageUrl);
```

## Provider Priority
1. **Together AI** (Default) - Fast, reliable
2. **Fal.ai** - Professional features
3. **Replicate** - Legacy support

## Common Issues & Solutions

### CORS Issues
Use proxy route for Together AI images:
```javascript
const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
```

### API Rate Limits
Implement retry with exponential backoff:
```javascript
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
    }
  }
}
```

### Memory Issues
Resize large images before adding to canvas:
```javascript
const maxDimension = 2048;
if (image.width > maxDimension || image.height > maxDimension) {
  const scale = Math.min(maxDimension / image.width, maxDimension / image.height);
  image.scale(scale);
}
```

## Testing Commands
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Build & Deploy
```bash
# Build for production
npm run build

# Start production server
npm start
```

## Key Features
- ✅ Multi-provider AI support
- ✅ Unified interface with tabs
- ✅ Client-side fallbacks
- ✅ Canvas integration
- ✅ Error handling & retries
- ✅ Image caching
- ✅ CORS proxy
- ✅ Rate limiting
- ✅ Style filters
- ✅ Background removal
- ✅ Image upscaling
- ✅ Image variations

## Browser Requirements
- Modern browser with ES2020 support
- WebGL2 for client-side AI features
- Canvas API support
- Fetch API support

## Performance Tips
1. Use image caching for repeated requests
2. Implement lazy loading for Transformers.js
3. Resize images before canvas operations
4. Use appropriate image formats (WebP when possible)
5. Implement request debouncing for user inputs

## Security Notes
- API keys are validated server-side
- User inputs are sanitized
- Rate limiting prevents abuse
- CORS policies restrict image access
- No sensitive data in client-side code

## Support & Resources
- **Together AI Docs**: https://docs.together.ai/
- **Fal.ai Docs**: https://fal.ai/docs
- **Replicate Docs**: https://replicate.com/docs
- **Fabric.js Docs**: http://fabricjs.com/docs/
- **Transformers.js**: https://huggingface.co/docs/transformers.js

## Version Compatibility
- Next.js: 15.2.4+
- React: 19.0.0+
- Fabric.js: 6.6.2+
- Node.js: 18.0.0+

This quick reference provides all the essential information needed to implement and maintain the AI tools system.
