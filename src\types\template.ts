// Template customization types

export interface EditableLayer {
  id: string;
  type: 'text' | 'image';
  name: string; // User-friendly name for the editable element
  originalValue?: string; // Original text content or image URL
  placeholder?: string; // Placeholder text for text layers
  constraints?: {
    maxLength?: number; // For text layers
    allowedFormats?: string[]; // For image layers (e.g., ['jpg', 'png'])
    maxFileSize?: number; // For image layers (in bytes)
  };
}

export interface TemplateCustomization {
  templateId: string;
  customizations: {
    [layerId: string]: {
      type: 'text' | 'image';
      value: string; // New text content or uploaded image URL
    };
  };
}

export interface CustomizationSession {
  id: string;
  templateId: string;
  customizations: TemplateCustomization['customizations'];
  previewUrl?: string;
  createdAt: Date;
  expiresAt: Date;
}

// Fabric.js object extensions for template system
export interface EditableFabricObject extends fabric.Object {
  id?: string;
  isEditable?: boolean;
  editableType?: 'text' | 'image';
  editableName?: string;
  editablePlaceholder?: string;
  editableConstraints?: EditableLayer['constraints'];
}

// API response types
export interface TemplateResponse {
  id: string;
  name: string;
  width: number;
  height: number;
  thumbnailUrl: string | null;
  json: string;
  editableLayers: EditableLayer[];
  isCustomizable: boolean;
  creator: {
    name: string | null;
    image: string | null;
  } | null;
  createdAt: string;
  updatedAt: string;
}

export interface CustomizationResponse {
  success: boolean;
  previewUrl?: string;
  downloadUrl?: string;
  sessionId?: string;
  error?: string;
}
