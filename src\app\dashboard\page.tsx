import { protectServer } from "@/features/auth/utils";

import { Banner } from "./(components)/banner";
import { ProjectsSection } from "./(components)/projects-section";
import { TemplatesSection } from "./(components)/templates-section";
import { CreateProjectModal } from "./(components)/create-project-modal";
import { PublicGalleryBanner } from "./(components)/public-gallery-banner";

export default async function DashboardPage() {
  await protectServer();

  return (
    <div className="flex flex-col space-y-6 max-w-screen-xl mx-auto pb-10">
      <Banner />
      <TemplatesSection />
      <PublicGalleryBanner />
      <ProjectsSection />
      <CreateProjectModal />
    </div>
  );
};
