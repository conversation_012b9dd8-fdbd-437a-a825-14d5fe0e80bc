"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

import { useCreateProject } from "@/features/projects/api/use-create-project";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  width?: number;
  height?: number;
  json?: string;
}

export const CreateProjectDialog = ({
  open,
  onOpenChange,
  width = 900,
  height = 1200,
  json = "",
}: CreateProjectDialogProps) => {
  const [name, setName] = useState("");
  const router = useRouter();
  const mutation = useCreateProject();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const projectName = name.trim() || "Untitled Project";

    mutation.mutate(
      {
        name: projectName,
        json,
        width,
        height,
      },
      {
        onSuccess: ({ data }) => {
          onOpenChange(false);
          setName("");
          router.push(`/editor/${data.id}`);
        },
      }
    );
  };

  const handleCancel = () => {
    onOpenChange(false);
    setName("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Give your project a name to get started.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter project name..."
                className="col-span-3"
                maxLength={50}
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? "Creating..." : "Create Project"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
