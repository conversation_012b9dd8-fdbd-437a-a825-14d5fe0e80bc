{"name": "the-canvas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "bunx drizzle-kit generate", "db:migrate": "bunx drizzle-kit migrate", "db:studio": "bunx drizzle-kit studio"}, "dependencies": {"@auth/core": "^0.34.1", "@auth/drizzle-adapter": "^1.4.1", "@fal-ai/client": "^1.6.1", "@hono/auth-js": "^1.0.10", "@hono/zod-validator": "^0.2.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.50.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^3.6.0", "drizzle-orm": "^0.31.4", "drizzle-zod": "^0.5.1", "fabric": "5.3.0-browser", "hono": "^4.4.12", "jsdom": "^24.1.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.399.0", "material-colors": "^1.2.6", "next": "14.2.4", "next-auth": "^5.0.0-beta.19", "next-themes": "^0.3.0", "postgres": "^3.4.7", "react": "^18", "react-color": "^2.19.3", "react-dom": "^18", "react-icons": "^5.2.1", "react-use": "^17.5.0", "replicate": "^0.31.1", "sonner": "^1.5.0", "stripe": "^16.2.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "together-ai": "^0.20.0", "unsplash-js": "^7.0.19", "use-file-picker": "^2.1.2", "uuidv4": "^6.2.13", "zod": "^3.23.8", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/fabric": "5.3.0", "@types/jest": "^29.5.8", "@types/lodash.debounce": "^4.0.9", "@types/material-colors": "^1.2.3", "@types/node": "^20", "@types/react": "^18", "@types/react-color": "^3.0.12", "@types/react-dom": "^18", "babel-jest": "^29.7.0", "dotenv": "^16.4.5", "drizzle-kit": "^0.22.8", "eslint": "^8", "eslint-config-next": "14.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "pg": "^8.12.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}