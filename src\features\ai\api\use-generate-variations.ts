import { useMutation } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { InferRequestType, InferResponseType } from "hono";

const $post = client.api.ai["generate-variations"].$post;

type RequestType = InferRequestType<typeof $post>["json"];
type ResponseType = InferResponseType<typeof $post>;

export const useGenerateVariations = () => {
  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      const response = await $post({ json });

      if (!response.ok) {
        throw new Error("Failed to generate variations");
      }

      return response.json();
    },
  });

  return mutation;
};
