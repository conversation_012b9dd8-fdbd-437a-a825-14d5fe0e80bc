# AI Integration with Multiple Providers - Next.js Poster Editor

This Next.js Poster Editor now includes comprehensive AI tools powered by multiple AI providers including Together AI, Fal.ai, and Replicate.

## Features Added

### 🎨 Unified AI Tools Panel
- **🪄 AI Tools**: Single unified panel with 4 tabs:
  - **Generate**: Create images from text prompts
  - **Edit**: Generate variations and apply style filters
  - **Enhance**: Background removal and image upscaling
  - **Settings**: Provider configuration and status

### 🤖 AI Providers & Models Supported

#### **Together AI (Default Provider)**
- **FLUX Kontext Max**: Latest FLUX model with context understanding
- **FLUX Kontext Dev**: FLUX development model with context
- **FLUX Schnell**: Fast FLUX model via Together AI

#### **Fal.ai**
- **FLUX Pro 1.1**: High-quality FLUX Pro model
- **FLUX Dev**: FLUX development model
- **BiRefNet**: Advanced background removal
- **Clarity Upscaler**: High-quality image upscaling

#### **Replicate (Legacy)**
- **FLUX Schnell**: Fast text-to-image generation
- **FLUX Dev**: High-quality text-to-image generation
- **Stable Diffusion**: Classic stable diffusion model
- **Background Removal**: Automatic background removal
- **Real-ESRGAN**: Image upscaling and enhancement

### ⚡ AI Features

#### **1. AI Generate Panel**
- **Multiple Models**: Choose between FLUX Schnell (fast), FLUX Dev (high quality), or Stable Diffusion
- **Custom Sizes**: Generate images from 512x512 to 1024x1024 pixels
- **Advanced Prompting**: Detailed text prompts for precise image generation
- **Canvas Integration**: Generated images are automatically added to the canvas

#### **2. AI Enhance Panel**
- **Background Removal**: AI-powered automatic background removal
- **Image Upscaling**: 2x, 4x, 8x scaling with Real-ESRGAN
- **Generate Variations**: Create variations of existing images with custom prompts
- **AI Style Filters**: 6 artistic styles (Artistic, Vintage, Cyberpunk, Watercolor, Oil Paint, Sketch)

## Setup Instructions

### 1. Dependencies Already Installed
The required AI provider SDKs have been installed:
```bash
npm install @fal-ai/client together-ai replicate
```

### 2. Configure API Keys
Update `.env.local` file with your AI provider API tokens:
```env
# Together AI (Default Provider)
TOGETHER_API_KEY="your-together-api-key-here"
NEXT_PUBLIC_TOGETHER_API_KEY="your-together-api-key-here"

# Fal.ai
FAL_KEY="your-fal-api-key-here"
NEXT_PUBLIC_FAL_KEY="your-fal-api-key-here"

# Replicate (Legacy)
REPLICATE_API_TOKEN="your-replicate-api-token-here"
NEXT_PUBLIC_REPLICATE_API_TOKEN="your-replicate-api-token-here"
```

### 3. Get API Tokens
#### **Together AI (Recommended)**
1. Sign up at [Together.ai](https://together.ai)
2. Go to your API settings
3. Generate an API key
4. Add it to your `.env.local` file

#### **Fal.ai**
1. Sign up at [Fal.ai](https://fal.ai)
2. Go to your account settings
3. Generate an API key
4. Add it to your `.env.local` file

#### **Replicate**
1. Sign up at [Replicate.com](https://replicate.com)
2. Go to your account settings
3. Generate an API token
4. Add it to your `.env.local` file

### 4. Start Development Server
```bash
npm run dev
```

## Usage

### Accessing AI Tools
1. Open the Poster Editor at `http://localhost:3000`
2. Look for the **🪄 AI Tools** button in the left sidebar
3. Click to open the unified AI panel with 4 tabs

### Using AI Tools

#### **Generate Tab**
1. Select an AI model (FLUX Kontext Max is default)
2. Enter a descriptive prompt
3. Choose image size (512x512 to 1024x1024)
4. Click "Generate Image"
5. The generated image will be added to your canvas

#### **Edit Tab**
1. Select an image on the canvas
2. **Generate Variations**: Add custom prompt and create variations
3. **AI Style Filters**: Apply artistic styles (Artistic, Vintage, Cyberpunk, etc.)

#### **Enhance Tab**
1. Select an image on the canvas
2. **Remove Background**: Automatically remove image background
3. **Upscale Image**: Increase resolution (2x, 4x, 8x)

#### **Settings Tab**
1. **Provider Selection**: Choose your preferred AI provider
2. **Provider Status**: Check which providers are configured
3. **Configuration Instructions**: View setup instructions for each provider

## File Structure

```
nextjs-poster-editor/
├── src/
│   ├── services/
│   │   └── aiService.js                    # Multi-provider AI integration
│   ├── components/
│   │   ├── editor/
│   │   │   ├── LeftSidebar.jsx            # Enhanced with unified AI tools
│   │   │   └── panels/
│   │   │       └── AiPanel.jsx            # Unified AI panel with tabs
│   │   └── ui/
│   │       └── textarea.jsx               # New UI component
│   └── store/
│       └── index.js                       # Enhanced with AI panel state
├── .env.local                             # Environment variables
└── AI_INTEGRATION.md                      # This documentation
```

## Technical Implementation

### Service Architecture
- **Multi-Provider AI Service**: `src/services/aiService.js` with support for Together AI, Fal.ai, and Replicate
- **Provider Abstraction**: Unified interface for different AI providers
- **Async Operations**: All AI operations use async/await pattern
- **Environment Variables**: Supports both server-side and client-side tokens
- **Error Handling**: Provider-specific error handling and user-friendly messages

### UI Components
- **AiPanel**: Unified AI interface with tabbed layout
- **Provider Management**: Dynamic provider selection and status checking
- **Textarea Component**: Enhanced UI component for prompt input
- **Loading States**: Provider-specific visual feedback during AI operations

### State Management
- **Zustand Store**: Enhanced with unified AI panel state
- **Panel Management**: Integrated with existing panel system
- **Canvas Integration**: Seamless Fabric.js canvas integration
- **Provider State**: Dynamic provider availability and configuration tracking

### Canvas Integration
- **Smart Positioning**: Generated images are positioned optimally
- **Object Replacement**: Enhanced images replace originals while preserving transformations
- **Layer Management**: Automatic layer updates and selection
- **Cross-Origin Support**: Proper handling of external image URLs

## API Configuration

### Environment Variables
The app supports multiple AI providers with both server-side and client-side API tokens:

**Together AI:**
- `TOGETHER_API_KEY`: Server-side token
- `NEXT_PUBLIC_TOGETHER_API_KEY`: Client-side token

**Fal.ai:**
- `FAL_KEY`: Server-side token
- `NEXT_PUBLIC_FAL_KEY`: Client-side token

**Replicate:**
- `REPLICATE_API_TOKEN`: Server-side token
- `NEXT_PUBLIC_REPLICATE_API_TOKEN`: Client-side token

### Configuration Detection
- **Multi-Provider Support**: Automatic detection of configured providers
- **Provider Priority**: Together AI > Fal.ai > Replicate
- **Dynamic Model Loading**: Only shows models from configured providers
- **User-friendly Warnings**: Clear messages when providers are not configured
- **Graceful Fallbacks**: Automatic provider switching when available

## Error Handling

### User Experience
- **Loading Indicators**: Spinners and progress states during operations
- **Error Messages**: Clear, actionable error messages
- **API Validation**: Checks for valid API configuration
- **Network Resilience**: Handles network errors gracefully

### Developer Experience
- **Console Logging**: Detailed logs for debugging
- **Error Boundaries**: Proper error containment
- **Type Safety**: JSX components with proper prop validation

## Performance Considerations

- **Async Operations**: Non-blocking AI operations
- **Image Optimization**: Efficient handling of generated images
- **Memory Management**: Proper cleanup of canvas objects
- **Caching**: Browser caching of generated images

## Troubleshooting

### Common Issues
1. **"Configure API Key" message**: Add valid provider tokens to `.env.local`
2. **Generation fails**: Check internet connection and API token validity
3. **Slow generation**: Try FLUX Kontext Max or FLUX Schnell for faster results
4. **Provider not available**: Check Settings tab for provider configuration status
5. **Canvas issues**: Ensure images have proper cross-origin headers

### Debug Information
- Check browser console for detailed error messages
- Use Settings tab to verify provider configuration status
- Check provider-specific error messages for troubleshooting
- Test API connectivity with simple requests

## Next Steps

### Potential Enhancements
- **More AI Models**: Add support for additional Replicate models
- **Batch Processing**: Generate multiple images at once
- **Style Transfer**: Advanced style transfer capabilities
- **Custom Models**: Support for user-trained models

### Integration Opportunities
- **Template Generation**: AI-powered template creation
- **Smart Layouts**: AI-assisted layout suggestions
- **Content Aware**: Context-aware image generation

## 🚀 **Getting Started**

### **To Start Using AI Features:**

1. **Get API Tokens**:
   - **Together AI** (Recommended): Sign up at [Together.ai](https://together.ai)
   - **Fal.ai**: Sign up at [Fal.ai](https://fal.ai)
   - **Replicate**: Sign up at [Replicate.com](https://replicate.com)

2. **Configure Environment**: Add your API tokens to `.env.local`

3. **Access AI Tools**: Click the **🪄 AI Tools** button in the left sidebar

4. **Configure Providers**: Use the Settings tab to check provider status

5. **Create & Enhance**:
   - Generate images from prompts using FLUX Kontext models
   - Edit images with variations and style filters
   - Enhance images with background removal and upscaling

The unified AI integration is complete and ready for production use! 🎉
