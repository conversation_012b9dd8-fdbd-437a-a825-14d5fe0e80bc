import { useState, useEffect } from "react";

import { usePaywall } from "@/features/subscriptions/hooks/use-paywall";

import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";

import { useGenerateImage } from "@/features/ai/api/use-generate-image";
import { addImageToCanvas } from "@/fabric/fabric-utils";
import { isApiConfigured, AI_PROVIDERS } from "@/services/aiService";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { AlertCircle, Loader2, Wand2 } from "lucide-react";

interface AiSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
};

export const AiSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: AiSidebarProps) => {
  const { shouldBlock, triggerPaywall } = usePaywall();
  const mutation = useGenerateImage();

  const [value, setValue] = useState("");
  const [options, setOptions] = useState({
    width: 1024,
    height: 1024,
    steps: 28,
    guidance: 7.5
  });
  const [apiConfigured, setApiConfigured] = useState(false);

  useEffect(() => {
    setApiConfigured(isApiConfigured());
  }, []);

  const onSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ) => {
    e.preventDefault();

    if (shouldBlock) {
      triggerPaywall();
      return;
    }

    if (!apiConfigured) {
      console.error('No AI providers configured');
      return;
    }

    mutation.mutate({
      prompt: value,
      ...options
    }, {
      onSuccess: async ({ data }) => {
        if (editor?.canvas && data) {
          try {
            await addImageToCanvas(editor.canvas, data);
          } catch (error) {
            console.error('Failed to add image to canvas:', error);
            // Fallback to original method
            editor?.addImage(data);
          }
        }
      }
    });
  };

  const onClose = () => {
    onChangeActiveTool("select");
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "ai" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="AI"
        description="Generate an image using AI"
      />
      <ScrollArea>
        <form onSubmit={onSubmit} className="p-4 space-y-6">
          <div className="space-y-2">
            <Label htmlFor="prompt">Prompt</Label>
            <Textarea
              id="prompt"
              disabled={mutation.isPending}
              placeholder="An astronaut riding a horse on mars, hd, dramatic lighting"
              rows={6}
              required
              minLength={3}
              value={value}
              onChange={(e) => setValue(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="width">Width</Label>
              <Input
                id="width"
                type="number"
                value={options.width}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  width: parseInt(e.target.value) || 1024
                }))}
                min={256}
                max={2048}
                step={64}
                disabled={mutation.isPending}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="height">Height</Label>
              <Input
                id="height"
                type="number"
                value={options.height}
                onChange={(e) => setOptions(prev => ({
                  ...prev,
                  height: parseInt(e.target.value) || 1024
                }))}
                min={256}
                max={2048}
                step={64}
                disabled={mutation.isPending}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Steps: {options.steps}</Label>
            <Slider
              value={[options.steps]}
              onValueChange={([value]) => setOptions(prev => ({
                ...prev,
                steps: value
              }))}
              min={10}
              max={50}
              step={1}
              disabled={mutation.isPending}
            />
          </div>

          <div className="space-y-2">
            <Label>Guidance: {options.guidance}</Label>
            <Slider
              value={[options.guidance]}
              onValueChange={([value]) => setOptions(prev => ({
                ...prev,
                guidance: value
              }))}
              min={1}
              max={20}
              step={0.5}
              disabled={mutation.isPending}
            />
          </div>

          {!apiConfigured && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                No AI providers configured. Please add API keys to your environment.
              </span>
            </div>
          )}

          <Button
            disabled={mutation.isPending || !apiConfigured || !value.trim()}
            type="submit"
            className="w-full"
          >
            {mutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                Generate Image
              </>
            )}
          </Button>
        </form>
      </ScrollArea>
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
